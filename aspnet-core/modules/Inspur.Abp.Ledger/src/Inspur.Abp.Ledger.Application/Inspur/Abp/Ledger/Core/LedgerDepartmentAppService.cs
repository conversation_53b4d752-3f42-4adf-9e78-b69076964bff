using System;
using System.Collections.Generic;
using System.IO;
using System.IO.Compression;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Inspur.Abp.Application.Extensions;
using Inspur.Abp.Ledger.Core.Args;
using Inspur.Abp.Ledger.Core.Dtos;
using Inspur.Abp.Ledger.Extensions;
using Inspur.Abp.Ledger.Settings;
using Inspur.Abp.Platform.Settings;
using Inspur.Abp.Platform.Extensions;
using Volo.Abp;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Users;
using IdentityRole = Volo.Abp.Identity.IdentityRole;
using Inspur.Abp.Platform.Organizational.Dtos;
using Microsoft.EntityFrameworkCore;
using IRegionRepository = Inspur.Abp.Ledger.RegionDepartments.IRegionRepository;
using Volo.Abp.EventBus.Distributed;
using Inspur.Abp.Ledger.Core.Etos;
using Volo.Abp.Identity;
using Inspur.Abp.Ledger.Core.QueryableExtensions;
using Inspur.Abp.Platform.Organizational;
using Inspur.Abp.Platform.WFTasks;
using JetBrains.Annotations;
using LINGYUN.Abp.Notifications;
using Microsoft.EntityFrameworkCore.Query.Internal;
using Microsoft.Extensions.Options;
using Microsoft.Extensions.Logging;
using NPOI.SS.UserModel;
using NPOI.XSSF.UserModel;
using TryCode.Abp.Excel;
using Volo.Abp.BackgroundJobs;
using Volo.Abp.Uow;

namespace Inspur.Abp.Ledger.Core;

public class LedgerDepartmentAppService : AbstractKeyCrudAppService<LedgerDepartment, LedgerDepartmentDto,
        LedgerDepartmentKey, LedgerDepartmentPagedAndSortedResultRequestDto, CreateUpdateLedgerDepartmentDto,
        CreateUpdateLedgerDepartmentDto>,
    ILedgerDepartmentAppService
{
    private readonly ILedgerDepartmentRepository _repository;
    private readonly IDepartmentRepository _departmentRepository;
    private readonly ILedgerUserRepository _ledgerUserRepository;
    private readonly IDepartmentAppService _departmentAppService;
    private readonly Platform.Users.IUserAppService _userAppService;
    private readonly IRepository<IdentityRole> _roleRepository;
    private readonly IRepository<IdentityUser> _identityUserRepository;
    private readonly IRegionRepository _regionRepository;
    private readonly ILedgerRepository _ledgerRepository;
    private readonly ILedgerDepartmentDapperRepository _ledgerDepartmentDapperRepository;
    private readonly IDistributedEventBus _distributedEventBus;
    private readonly LedgerDepartmentCacheManage _ledgerDepartmentCacheManage;
    private readonly IDepartmentDapperRepository _departmentDapperRepository;
    private readonly ILedgerRunwayRelationRepository _ledgerRunwayRelationRepository;
    private readonly LedgerDepartmentManager _ledgerDepartmentManager;
    private readonly LedgerCacheManager _ledgerCacheManager;
    private readonly LedgerOptions _ledgerOptions;
    private readonly IExcelExporter _excelExporter;
    private readonly ILedgerDepartmentDataDetailStatisticsRepository _ledgerDepartmentDataDetailStatisticsRepository;
    private readonly Inspur.Abp.Platform.Users.IUserAppService _userService;
    private readonly ILedgerPermissionsAuthorizationModeRepository _ledgerPermissionsAuthorizationModeRepository;
    private readonly IBackgroundJobManager _backgroundJobManager;
    private readonly IUnitOfWorkManager _unitOfWorkManager;
    private readonly IWFTaskManager _wfTaskManager;

    private readonly ILedgerDepartmentDataDetailStatisticsDapperRepository
        _ledgerDepartmentDataDetailStatisticsDapperRepository;

    public LedgerDepartmentAppService(ILedgerDepartmentRepository repository,
        IDepartmentRepository departmentRepository,
        ILedgerUserRepository ledgerUserRepository,
        IRepository<IdentityRole> roleRepository,
        IDepartmentAppService departmentAppService,
        Platform.Users.IUserAppService userAppService,
        IRegionRepository regionRepository, ILedgerRepository ledgerRepository,
        ILedgerDepartmentDapperRepository ledgerDepartmentDapperRepository, IDistributedEventBus distributedEventBus
        , IRepository<IdentityUser> identityUserRepository,
        LedgerDepartmentCacheManage ledgerDepartmentCacheManage,
        IDepartmentDapperRepository departmentDapperRepository,
        ILedgerRunwayRelationRepository ledgerRunwayRelationRepository,
        LedgerDepartmentManager ledgerDepartmentManager, LedgerCacheManager ledgerCacheManager,
        IOptions<LedgerOptions> ledgerOptions, IExcelExporter excelExporter,
        ILedgerDepartmentDataDetailStatisticsRepository ledgerDepartmentDataDetailStatisticsRepository,
        Platform.Users.IUserAppService userService,
        ILedgerPermissionsAuthorizationModeRepository ledgerPermissionsAuthorizationModeRepository,
        IUnitOfWorkManager unitOfWorkManager,
        IBackgroundJobManager backgroundJobManager, IWFTaskManager wfTaskManager,
        ILedgerDepartmentDataDetailStatisticsDapperRepository ledgerDepartmentDataDetailStatisticsDapperRepository) :
        base(repository)
    {
        _repository = repository;
        _departmentRepository = departmentRepository;
        _roleRepository = roleRepository;
        _ledgerUserRepository = ledgerUserRepository;
        _departmentAppService = departmentAppService;
        _userAppService = userAppService;
        _regionRepository = regionRepository;
        _ledgerRepository = ledgerRepository;
        _ledgerDepartmentDapperRepository = ledgerDepartmentDapperRepository;
        _distributedEventBus = distributedEventBus;
        _identityUserRepository = identityUserRepository;
        _ledgerDepartmentCacheManage = ledgerDepartmentCacheManage;
        _departmentDapperRepository = departmentDapperRepository;
        _ledgerRunwayRelationRepository = ledgerRunwayRelationRepository;
        _ledgerDepartmentManager = ledgerDepartmentManager;
        _ledgerCacheManager = ledgerCacheManager;
        _excelExporter = excelExporter;
        _ledgerDepartmentDataDetailStatisticsRepository = ledgerDepartmentDataDetailStatisticsRepository;
        _userService = userService;
        _ledgerPermissionsAuthorizationModeRepository = ledgerPermissionsAuthorizationModeRepository;
        _ledgerOptions = ledgerOptions.Value;
        _backgroundJobManager = backgroundJobManager;
        _wfTaskManager = wfTaskManager;
        _ledgerDepartmentDataDetailStatisticsDapperRepository = ledgerDepartmentDataDetailStatisticsDapperRepository;
        _unitOfWorkManager = unitOfWorkManager;
    }

    protected override Task DeleteByIdAsync(LedgerDepartmentKey id)
    {
        return _repository.DeleteAsync(e =>
            e.LedgerId == id.LedgerId &&
            e.DepartmentId == id.DepartmentId
        );
    }

    protected override async Task<LedgerDepartment> GetEntityByIdAsync(LedgerDepartmentKey id)
    {
        return await AsyncExecuter.FirstOrDefaultAsync(
            (await _repository.WithDetailsAsync()).Where(e =>
                e.LedgerId == id.LedgerId &&
                e.DepartmentId == id.DepartmentId
            )
        );
    }

    protected override IQueryable<LedgerDepartment> ApplyDefaultSorting(IQueryable<LedgerDepartment> query)
    {
        return query.OrderBy(e => e.LedgerId);
    }

    /// <summary>
    /// 台账跨部门授权列表
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    public override async Task<PagedResultDto<LedgerDepartmentDto>> GetListAsync(
        LedgerDepartmentPagedAndSortedResultRequestDto input)
    {
        //台账超级管理员可查看所有授权部门，管理部门可以查看管理的台账所有授权部门，普通数据领导只能查看自己授权的部门
        var query = await _repository.WithDetailsAsync(x => x.Department);
        IQueryable<LedgerDepartment> ledgerDepartmentQueryable;
        var roles = CurrentUser.Roles;
        var businessRole = CurrentUser.FindBusinessRole();
        //市级部门授权
        if (input.IsCounty is true)
        {
            ledgerDepartmentQueryable = query.WhereIf(input.LedgerId != null, x => x.LedgerId == input.LedgerId)
                .Where(s => s.County == true);

            ledgerDepartmentQueryable = ledgerDepartmentQueryable
                .WhereIf(input.RegionName != null, x => x.RegionName.Contains(input.RegionName));
            var ledgerDepartmentList = ledgerDepartmentQueryable.AsEnumerable()
                .DistinctBy(x => x.RegionName).OrderByDescending(x => x.CreationTime).ToList();
            var count = ledgerDepartmentList.Count;
            var items = ledgerDepartmentList.Skip(input.SkipCount).Take(input.MaxResultCount).ToList();
            var ledgerDepartmentDto = ObjectMapper.Map<List<LedgerDepartment>, List<LedgerDepartmentDto>>(items);
            foreach (var item in ledgerDepartmentDto)
            {
                //如果当前人是创建台账部门用户，则显示响应操作
                if (item.CreatorId == CurrentUser.GetId())
                {
                    item.IsSendResponseOperation = true;
                }
            }

            return new PagedResultDto<LedgerDepartmentDto>(count, ledgerDepartmentDto);
        }
        else
        {
            if (roles.Contains(RoleSettings.LedgerAdmin))
            {
                ledgerDepartmentQueryable = query.Include(x => x.Department)
                    .Include(x => x.AuthDepartment)
                    .WhereIf(input.LedgerId.HasValue, x => x.LedgerId == input.LedgerId);
            }
            else if (roles.Contains(RoleSettings.CountyLedgerAdmin) ||
                     businessRole.Contains(RoleSettings.CountyLedgerAdmin))
            {
                var regionDto =
                    await _userAppService.GetUserRegionAsync(CurrentUser.GetId(), CurrentUser.GetDepartmentId());
                var regions = (await _regionRepository.WithDetailsAsync())
                    .Where(x => x.TreeCode.StartsWith(regionDto.TreeCode)).OrderBy(x => x.TreeCode).ToList();
                var departmentExtendIds =
                    await _departmentAppService.GetDepartmentExtendIdsByRegionsAsync(regions.Select(x => x.Id)
                        .ToList());
                ledgerDepartmentQueryable = query.Include(x => x.Department)
                    .Include(x => x.AuthDepartment)
                    .WhereIf(input.LedgerId.HasValue, x => x.LedgerId == input.LedgerId)
                    .WhereIf(departmentExtendIds.Any(), x => departmentExtendIds.Contains(x.DepartmentId));
            }
            else
            {
                ledgerDepartmentQueryable = query.Include(x => x.Department)
                    .Include(x => x.AuthDepartment)
                    .Where(x => x.AuthDepartmentId == CurrentUser.GetBigDepartmentId() ||
                                x.CreatorId == CurrentUser.GetId())
                    .WhereIf(input.LedgerId != null, x => x.LedgerId == input.LedgerId);
            }

            var ledgerDepartmentDos = ledgerDepartmentQueryable.OrderByDescending(x => x.CreationTime)
                .WhereByIsResponse(input.IsResponse)
                .WhereIfByDepartmentName(input.DepartmentName)
                .WhereIfByDepartmentId(input.DepartmentId)
                .Where(s => s.County != true);
            var queryable = ledgerDepartmentDos.PageBy(input);
            var count = await AsyncExecuter.CountAsync(ledgerDepartmentDos);
            var items = await AsyncExecuter.ToListAsync(queryable);

            List<Guid?> regionIds = new List<Guid?>();
            var departmentRegionIds = items.Select(x => x.Department.RegionId).Distinct().ToList();
            var authDepartmentRegionIds = items.Select(x => x.AuthDepartment?.RegionId).Distinct().ToList();
            regionIds.AddRange(departmentRegionIds);
            regionIds.AddRange(authDepartmentRegionIds);
            regionIds = regionIds.Distinct().ToList();
            var regionList = await AsyncExecuter.ToListAsync(
                (await _regionRepository.WithDetailsAsync())
                .Where(x => regionIds.Contains(x.Id)));


            var ledgerDepartmentDto = ObjectMapper.Map<List<LedgerDepartment>, List<LedgerDepartmentDto>>(items);

            foreach (var item in ledgerDepartmentDto)
            {
                //如果当前人是创建台账部门用户，则显示响应操作
                if (item.UserId == CurrentUser.GetId())
                {
                    item.IsSendResponseOperation = true;
                }

                item.RegionName = regionList.FirstOrDefault(x => x.Id == item.Department.RegionId)?.Name;
                item.AuthRegionName = regionList.FirstOrDefault(x => x.Id == item.AuthDepartment?.RegionId)?.Name;
            }

            return new PagedResultDto<LedgerDepartmentDto>(count, ledgerDepartmentDto);
        }
    }

    /// <summary>
    /// 根据被授权部门查询台账列表
    /// </summary>
    /// <returns></returns>
    public async Task<PagedResultDto<LedgerDepartmentSmallDto>>
        GetListByBeAuthDepartmentId(Guid beAuthDepartmentId,
            GetListByBeAuthDepartmentIdRequestDto input)
    {
        var queryable = await _repository.WithDetailsAsync(x => x.Ledger.LedgerType);

        queryable = queryable
            .WhereByLedgerNotDeleted()
            .WhereByIsResponse(input.IsResponse)
            .WhereByLedgerName(input.LedgerName)
            .WhereByLedgerRunway(input.Runway)
            .WhereByLedgerIsOnline(input.IsOnline)
            .WhereByPermissions(input.Permissions)
            .WhereByTime(input.StartTime, input.EndTime);

        // 台账管理员、台账超级管理员、区县台账运维员可以查看所有授权部门
        if (CurrentUser.IsInRole(RoleSettings.Admin) || CurrentUser.IsInRole(RoleSettings.LedgerAdmin) ||
            CurrentUser.FindBusinessRole().Contains(RoleSettings.CountyLedgerAdmin))
            queryable = queryable
                .WhereByDepartment(beAuthDepartmentId);
        else
            queryable = queryable
                .WhereByAuthorizationDepartmentAndBeDepartment(CurrentUser.GetBigDepartmentId(), beAuthDepartmentId);

        var count = await AsyncExecuter.CountAsync(queryable);

        if (count == 0)
            return new PagedResultDto<LedgerDepartmentSmallDto>();

        queryable = queryable.ApplySorting(input).PageBy(input);

        var items = await AsyncExecuter.ToListAsync(queryable);

        var itemsDto =
            ObjectMapper
                .Map<List<LedgerDepartment>, List<LedgerDepartmentSmallDto>>(items);

        var publishDepartmentIds = itemsDto
            .Where(x => x.Ledger.LedgerType != null && x.Ledger.LedgerType.DepartmentId.HasValue)
            .Select(s => s.Ledger.LedgerType.DepartmentId.Value).ToList();

        var publishDepartments =
            await _departmentDapperRepository.GetRegionDepartmentAndExtendListAsync(publishDepartmentIds);

        foreach (var itemDto in itemsDto.Where(x =>
                     x.Ledger.LedgerType != null && x.Ledger.LedgerType.DepartmentId.HasValue))
        {
            var publishDepartment =
                publishDepartments.FirstOrDefault(f => f.Id == itemDto.Ledger.LedgerType.DepartmentId);
            if (publishDepartment == null)
                continue;

            itemDto.Ledger.LedgerType.RegionId = publishDepartment.RegionId;
            itemDto.Ledger.LedgerType.RegionName = publishDepartment.RegionName;
            itemDto.Ledger.LedgerType.DepartmentName = publishDepartment.Name;
        }

        return new PagedResultDto<LedgerDepartmentSmallDto>(count, itemsDto);
    }

    /// <summary>
    /// 台账授权跨部门字段授权
    /// </summary>
    /// <param name="ledgerId"></param>
    /// <param name="input"></param>
    /// <returns></returns>
    public async Task CreateDepartmentDataAuthAsync(Guid ledgerId, LedgerDepartmentDateAuthDto input)
    {
        var ledger = await _ledgerCacheManager.GetLedgerCacheAsync(ledgerId);
        var currentLedgerDepartment = await _repository.GetAsync(x =>
            x.LedgerId == ledgerId && x.DepartmentId == CurrentUser.GetBigDepartmentId(), false);
        foreach (var departmentId in input.DepartmentIds)
        {
            var ledgerDepartment =
                await _repository.GetAsync(x => x.LedgerId == ledgerId && x.DepartmentId == departmentId);
            //如果是市级取消台账授权
            if (ledgerDepartment.County == true)
            {
                var countyLedgerDepartments = await _repository.GetListAsync(x =>
                    x.LedgerId == ledgerId && x.RegionName == ledgerDepartment.RegionName);
                foreach (var item in countyLedgerDepartments)
                {
                    currentLedgerDepartment.AuthOtherDepartment(item, input.TableFieldIds,
                        input.EditTableFieldIds, _ledgerOptions, ledger);
                    await _repository.UpdateAsync(item);
                }
            }
            else
            {
                currentLedgerDepartment.AuthOtherDepartment(ledgerDepartment, input.TableFieldIds,
                    input.EditTableFieldIds, _ledgerOptions, ledger);
                await _repository.UpdateAsync(ledgerDepartment);
            }
        }
    }

    /// <summary>
    /// 批量取消台账跨部门授权
    /// </summary>
    /// <param name="ledgerId"></param>
    /// <param name="input"></param>
    /// <returns></returns>
    public async Task RemoveDepartmentAsync(Guid ledgerId, LedgerDepartmentDateAuthDto input)
    {
        foreach (Guid departmentId in input.DepartmentIds)
        {
            LedgerDepartment ledgerDepartment = await _repository.FindAsync(x =>
                x.LedgerId == ledgerId && x.DepartmentId == departmentId);
            if (ledgerDepartment == null || ledgerDepartment.ManageLedgerDepartment) continue;
            {
                if (ledgerDepartment.County == true)
                {
                    var countyLedgerDepartments = await _repository.GetListAsync(x =>
                        x.LedgerId == ledgerId && x.RegionName == ledgerDepartment.RegionName);
                    foreach (var item in countyLedgerDepartments)
                    {
                        var departments = await GetAllChildDepartmentIdsAsync(ledgerId, item.DepartmentId, 5);

                        var departmentIds = departments.Select(x => x.DepartmentId).ToList();
                        var ledgerUsers = await _ledgerUserRepository.GetListAsync(
                            x => x.LedgerId == ledgerId && departmentIds.Contains(x.DepartmentId));
                        //删除当前部门授权过的子部门和子部门的人员
                        await _repository.DeleteManyAsync(departments);
                        await _ledgerUserRepository.DeleteManyAsync(ledgerUsers);

                        #region 增加授权模式 删除子部门

                        var parentAuthModes = await _ledgerPermissionsAuthorizationModeRepository.GetListAsync(x =>
                            departmentIds.Contains(x.BigDepartmentId) && x.LedgerId == ledgerId);
                        foreach (var authMode in parentAuthModes)
                        {
                            await _ledgerPermissionsAuthorizationModeRepository.HardDeleteAsync(authMode);
                        }

                        #endregion

                        //删除当前部门和当前部门下授权的人员
                        await _ledgerUserRepository.DeleteAsync(x =>
                            x.LedgerId == ledgerId && x.DepartmentId == item.DepartmentId);
                        await _repository.DeleteAsync(item, true);

                        #region 增加授权模式 删除当前部门

                        var authModes = await _ledgerPermissionsAuthorizationModeRepository.GetListAsync(x =>
                            x.LedgerId == ledgerId && x.BigDepartmentId == departmentId);
                        foreach (var authMode in authModes)
                        {
                            await _ledgerPermissionsAuthorizationModeRepository.HardDeleteAsync(authMode);
                        }

                        #endregion
                    }
                }
                else
                {
                    var departments = await GetAllChildDepartmentIdsAsync(ledgerId, departmentId, 5);

                    var departmentIds = departments.Select(x => x.DepartmentId).ToList();
                    var ledgerUsers = await _ledgerUserRepository.GetListAsync(
                        x => x.LedgerId == ledgerId && departmentIds.Contains(x.DepartmentId));
                    //删除当前部门授权过的子部门和子部门的人员
                    await _repository.DeleteManyAsync(departments);
                    await _ledgerUserRepository.DeleteManyAsync(ledgerUsers);

                    #region 增加授权模式 删除子部门

                    var parentAuthModes = await _ledgerPermissionsAuthorizationModeRepository.GetListAsync(x =>
                        departmentIds.Contains(x.BigDepartmentId) && x.LedgerId == ledgerId);
                    foreach (var authMode in parentAuthModes)
                    {
                        await _ledgerPermissionsAuthorizationModeRepository.HardDeleteAsync(authMode);
                    }

                    #endregion

                    //删除当前部门和当前部门下授权的人员
                    await _ledgerUserRepository.DeleteAsync(x =>
                        x.LedgerId == ledgerId && x.DepartmentId == departmentId);
                    await _repository.DeleteAsync(ledgerDepartment);

                    #region 增加授权模式 删除当前部门

                    var authModes = await _ledgerPermissionsAuthorizationModeRepository.GetListAsync(x =>
                        x.LedgerId == ledgerId && x.BigDepartmentId == departmentId);
                    foreach (var authMode in authModes)
                    {
                        await _ledgerPermissionsAuthorizationModeRepository.HardDeleteAsync(authMode);
                    }

                    #endregion
                }

                // 版本上线时开启
                // 更新删除 填报进度的部门记录。及统计部门
                await _backgroundJobManager.EnqueueAsync(new LedgerFillDeleteStatisticsArgs
                {
                    LedgerId = ledgerId,
                    DepartmentId = departmentId,
                    AuthDepartmentId = ledgerDepartment.AuthDepartmentId
                });
            }
            //删除跑道和业务表关联
            var department = await _departmentRepository.FindAsync(x => x.Id == departmentId);
            if (department?.RegionId == null) continue;
            {
                var ledgerRunwayRelations = await _ledgerRunwayRelationRepository.GetListAsync(x =>
                    x.LedgerId == ledgerId && x.RegionId == department.RegionId);
                foreach (var relation in ledgerRunwayRelations)
                {
                    await _ledgerRunwayRelationRepository.HardDeleteAsync(relation);
                }
            }
        }
    }

    private async Task<List<LedgerDepartment>> GetDirectChildDepartmentIdsAsync(Guid ledgerId, Guid departmentId)
    {
        var ledgerDepartments = await _repository.GetListAsync(x =>
            x.LedgerId == ledgerId && x.AuthDepartmentId == departmentId && x.DepartmentId != departmentId);
        return ledgerDepartments;
    }

    private async Task<List<LedgerDepartment>> GetAllChildDepartmentIdsAsync(Guid ledgerId, Guid departmentId,
        int maxDepth, int currentDepth = 0)
    {
        // 获取直接子级ID  
        var ledgerDepartments = await GetDirectChildDepartmentIdsAsync(ledgerId, departmentId);
        var ledgerDepartmentList = new List<LedgerDepartment>(ledgerDepartments);

        // 如果当前深度已经达到最大深度，则返回已经找到的列表  
        if (currentDepth >= maxDepth)
        {
            return ledgerDepartments.Any() ? ledgerDepartmentList : new List<LedgerDepartment>();
        }

        // 递归调用以获取更深层级的子级ID，但不超过最大深度  
        foreach (var ledgerDepartment in ledgerDepartments)
        {
            var childChildIds =
                await GetAllChildDepartmentIdsAsync(ledgerId, ledgerDepartment.DepartmentId, maxDepth,
                    currentDepth + 1); // 递归调用，深度+1    
            ledgerDepartmentList.AddRange(childChildIds); // 将子级的子级添加到列表中    
        }

        return ledgerDepartmentList;
    }

    /// <summary>
    /// 获取台账部门下所有子部门
    /// </summary>
    /// <param name="ledgerId"></param>
    /// <param name="departmentId"></param>
    /// <param name="maxDepth"></param>
    /// <param name="currentDepth"></param>
    /// <returns></returns>
    public async Task<List<LedgerDepartment>> GetAllChildDepartmentAsync(Guid ledgerId, Guid departmentId, int maxDepth,
        int currentDepth = 0)
    {
        // 获取直接子级ID  
        var ledgerDepartments = await GetDirectChildDepartmentIdsAsync(ledgerId, departmentId);
        var ledgerDepartmentList = new List<LedgerDepartment>(ledgerDepartments);

        // 如果当前深度已经达到最大深度，则返回已经找到的列表  
        if (currentDepth >= maxDepth)
        {
            return ledgerDepartments.Any() ? ledgerDepartmentList : new List<LedgerDepartment>();
        }

        // 递归调用以获取更深层级的子级ID，但不超过最大深度  
        foreach (var ledgerDepartment in ledgerDepartments)
        {
            var childChildIds =
                await GetAllChildDepartmentIdsAsync(ledgerId, ledgerDepartment.DepartmentId, maxDepth,
                    currentDepth + 1); // 递归调用，深度+1    
            ledgerDepartmentList.AddRange(childChildIds); // 将子级的子级添加到列表中    
        }

        return ledgerDepartmentList;
    }

    /// <summary>
    /// 查询台账跨部门授权中的操作权限
    /// </summary>
    /// <param name="ledgerId"></param>
    /// <param name="departmentId"></param>
    /// <returns></returns>
    public async Task<List<LedgerPermissionType>> GetDepartmentPermissionsAsync(Guid ledgerId, Guid departmentId)
    {
        var ledgerDepartment = await _repository.FindAsync
            (x => x.LedgerId == ledgerId && x.DepartmentId == departmentId);
        if (ledgerDepartment != null)
        {
            if (ledgerDepartment.Permissions != null) return ledgerDepartment.Permissions;
            ledgerDepartment.Permissions = Enum.GetValues(typeof(LedgerPermissionType))
                .Cast<LedgerPermissionType>().ToList();
            await _repository.UpdateAsync(ledgerDepartment);
            return ledgerDepartment.Permissions;
        }
        else
        {
            return Enum.GetValues(typeof(LedgerPermissionType))
                .Cast<LedgerPermissionType>().ToList();
        }
    }

    /// <summary>
    /// 查询台账跨部门授权中的操作权限
    /// </summary>
    /// <param name="ledgerId"></param>
    /// <param name="departmentId"></param>
    /// <returns></returns>
    public async Task<LedgerDepartmentAuthPermissionDto> GetDepartmentPermissionAsync(Guid ledgerId, Guid departmentId)
    {
        var ledgerDepartment = await _repository.FindAsync
            (x => x.LedgerId == ledgerId && x.DepartmentId == departmentId);
        if (ledgerDepartment == null)
            return null;
        return new LedgerDepartmentAuthPermissionDto()
        {
            Permissions = ledgerDepartment.Permissions,
            EditTableFieldIds = ledgerDepartment.EditTableFieldIds,
            TableFieldIds = ledgerDepartment.TableFieldIds
        };
    }

    /// <summary>
    /// 批量修改台账跨部门授权中的操作权限
    /// </summary>
    /// <param name="ledgerId"></param>
    /// <param name="input"></param>
    /// <returns></returns>
    public async Task UpdateDepartmentPermissionsAsync(Guid ledgerId, LedgerDepartmentDateAuthDto input)
    {
        foreach (var departmentId in input.DepartmentIds)
        {
            if (input.IsCounty is true)
            {
                var ledgerDepartment = await _repository.FindAsync(x =>
                    x.LedgerId == ledgerId && x.DepartmentId == departmentId);
                if (ledgerDepartment != null)
                {
                    if (ledgerDepartment.County == true)
                    {
                        var countyLedgerDepartments = await _repository.GetListAsync(x =>
                            x.LedgerId == ledgerId && x.RegionName == ledgerDepartment.RegionName);
                        foreach (var item in countyLedgerDepartments)
                        {
                            item.Permissions = input.Permissions;
                            await _repository.UpdateAsync(item);

                            //修改该部门下面所有授权的人员权限（查看和审核权限是每个部门前端默认勾选）
                            var ledgerUsers =
                                await _ledgerUserRepository.GetListAsync(x =>
                                    x.LedgerId == ledgerId && x.DepartmentId == item.DepartmentId);
                            foreach (var ledgerUser in ledgerUsers)
                            {
                                var exceptPermissionTypes = ledgerUser.Permissions.Except(input.Permissions).ToList();
                                var intersectPermissionTypes =
                                    ledgerUser.Permissions.Intersect(input.Permissions).ToList();
                                if (exceptPermissionTypes.Count == 0) continue;
                                ledgerUser.Permissions = intersectPermissionTypes;
                                await _ledgerUserRepository.UpdateAsync(ledgerUser);
                            }
                        }
                    }
                    else
                    {
                        ledgerDepartment.Permissions = input.Permissions;
                        await _repository.UpdateAsync(ledgerDepartment);
                        //修改该部门下面所有授权的人员权限（查看和审核权限是每个部门前端默认勾选）
                        var ledgerUsers =
                            await _ledgerUserRepository.GetListAsync(x =>
                                x.LedgerId == ledgerId && x.DepartmentId == departmentId);
                        foreach (var ledgerUser in ledgerUsers)
                        {
                            var exceptPermissionTypes = ledgerUser.Permissions.Except(input.Permissions).ToList();
                            var intersectPermissionTypes = ledgerUser.Permissions.Intersect(input.Permissions).ToList();
                            if (exceptPermissionTypes.Count == 0) continue;
                            ledgerUser.Permissions = intersectPermissionTypes;
                            await _ledgerUserRepository.UpdateAsync(ledgerUser);
                        }
                    }
                }
            }
            else
            {
                //修改部门操作权限前先判断它有没有给它部门的下级部门授权如果有，那么就不能填报修改成查阅
                var hasChildPermission = await _repository.AnyAuthDepartmentAsync(ledgerId, departmentId);
                //判断是否修改子部门单个权限
                var singeHasChildPermission =
                    await _repository.AnyAuthDepartmentAsync(ledgerId, CurrentUser.GetBigDepartmentId(), departmentId);
                //填报，查阅-导出，不能修改成查阅。 查阅可以修改成填报，所以只判断前端传值是否只有{0}
                var permissions = new List<LedgerPermissionType> { LedgerPermissionType.View };
                var sequenceEqual = input.Permissions.SequenceEqual(permissions);

                if (!hasChildPermission || !sequenceEqual)
                {
                    //修改该部门下面所有授权部门的权限
                    //判断是否存在下级部门授权
                    if (hasChildPermission && singeHasChildPermission)
                    {
                        //查询该台账授权的部门，且是下级部门数据
                        //获取该部门下所有部门id
                        var departments = await GetAllChildDepartmentIdsAsync(ledgerId, departmentId, 5);

                        //获取部门id集合
                        var departmentIds = departments.Select(x => x.DepartmentId).ToList();
                        //添加当前部门id到集合
                        departmentIds.Add(departmentId);

                        foreach (var deptId in departmentIds)
                        {
                            Logger.LogInformation("联动下级修改权限");
                            //重写当前权限
                            var rewritePermissions = deptId == departmentId
                                ? input.Permissions
                                : (departments.Where(x => x.DepartmentId == deptId).FirstOrDefault().Permissions)
                                .Intersect(input.Permissions).ToList();
                            var ledgerDepartment =
                                await _repository.FirstOrDefaultAsync(x =>
                                    x.LedgerId == ledgerId && x.DepartmentId == deptId);
                            //重写权限
                            if (ledgerDepartment != null)
                            {
                                ledgerDepartment.Permissions = rewritePermissions;
                                await _repository.UpdateAsync(ledgerDepartment);
                            }

                            Logger.LogInformation("ledgerDepartment 已操作");
                            //重写个人权限
                            var ledgerUsers = await _ledgerUserRepository.GetListAsync(x =>
                                x.LedgerId == ledgerId && x.DepartmentId == deptId);
                            foreach (var ledgerUser in ledgerUsers)
                            {
                                var newPermissions = ledgerUser.Permissions.Intersect(input.Permissions).Distinct()
                                    .ToList();
                                if (newPermissions.Any())
                                {
                                    ledgerUser.Permissions = newPermissions;
                                    await _ledgerUserRepository.UpdateAsync(ledgerUser);
                                }
                                else
                                {
                                    await _ledgerUserRepository.DeleteAsync(ledgerUser);
                                }
                            }

                            #region 增加授权模式 修改

                            var authModes =
                                await _ledgerPermissionsAuthorizationModeRepository.GetListAsync(x =>
                                    x.BigDepartmentId == deptId && x.LedgerId == ledgerId);

                            foreach (var authMode in authModes)
                            {
                                var newPermissions = authMode.Permissions.Intersect(input.Permissions).Distinct()
                                    .ToList();
                                if (newPermissions.Any())
                                {
                                    authMode.Permissions = newPermissions;
                                    await _ledgerPermissionsAuthorizationModeRepository.UpdateAsync(authMode);
                                }
                                else
                                {
                                    await _ledgerPermissionsAuthorizationModeRepository.HardDeleteAsync(authMode);
                                }
                            }

                            #endregion

                            Logger.LogInformation("ledgerUsers 已操作");
                        }
                    }
                    else if (singeHasChildPermission)
                    {
                        Logger.LogInformation("修改单个权限");
                        var ledgerDepartment = await _repository.FirstOrDefaultAsync(x =>
                            x.LedgerId == ledgerId && x.DepartmentId == departmentId);
                        //重写权限
                        if (ledgerDepartment != null)
                        {
                            ledgerDepartment.Permissions = input.Permissions;
                            await _repository.UpdateAsync(ledgerDepartment);
                        }

                        Logger.LogInformation("ledgerDepartment 已操作");
                        var ledgerUsers = await _ledgerUserRepository.GetListAsync(x =>
                            x.LedgerId == ledgerId && x.DepartmentId == departmentId);

                        foreach (var ledgerUser in ledgerUsers)
                        {
                            var newPermissions = ledgerUser.Permissions.Intersect(input.Permissions).Distinct()
                                .ToList();
                            if (newPermissions.Any())
                            {
                                ledgerUser.Permissions = newPermissions;
                                await _ledgerUserRepository.UpdateAsync(ledgerUser);
                            }
                            else
                            {
                                await _ledgerUserRepository.DeleteAsync(ledgerUser);
                            }
                        }

                        #region 增加授权模式 修改

                        var authModes =
                            await _ledgerPermissionsAuthorizationModeRepository.GetListAsync(x =>
                                x.BigDepartmentId == departmentId && x.LedgerId == ledgerId);

                        foreach (var authMode in authModes)
                        {
                            var newPermissions = authMode.Permissions.Intersect(input.Permissions).Distinct()
                                .ToList();
                            if (newPermissions.Any())
                            {
                                authMode.Permissions = newPermissions;
                                await _ledgerPermissionsAuthorizationModeRepository.UpdateAsync(authMode);
                            }
                            else
                            {
                                await _ledgerPermissionsAuthorizationModeRepository.HardDeleteAsync(authMode);
                            }
                        }

                        #endregion


                        Logger.LogInformation("ledgerUsers 已操作");
                    }
                    else
                    {
                        throw new UserFriendlyException("该部门数据领导已授权其他子部门，不能向下修改权限");
                    }
                }
                else
                {
                    throw new UserFriendlyException("该部门数据领导已授权其他子部门，不能向下修改权限");
                }
            }
        }
    }

    /// <summary>
    /// 效验批量本部门授权和复用权限时所选台账的操作权限是否一致
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    public async Task ValidationOperateAsync(LedgerDepartmentDateAuthDto input)
    {
        var ledgerUsers = new List<LedgerUser>();
        foreach (var ledgerId in input.LedgerIds)
        {
            var ledgerUser = await _ledgerUserRepository.FindAsync(x =>
                x.LedgerId == ledgerId && x.DepartmentId == CurrentUser.GetBigDepartmentId() &&
                x.UserId == CurrentUser.GetId());
            if (ledgerUser != null)
            {
                ledgerUsers.Add(ledgerUser);
            }
        }

        // 假设第一个对象的Permissions作为基准  
        if (ledgerUsers.Count > 0)
        {
            var basePermissions = ledgerUsers[0].Permissions;
            if (basePermissions != null)
            {
                basePermissions = basePermissions.Distinct().OrderBy(x => x).ToList();
                // 检查其他对象的Permissions是否与基准一致  
                foreach (var item in ledgerUsers)
                {
                    var permissions = item.Permissions;
                    if (permissions != null)
                    {
                        permissions = permissions.Distinct().OrderBy(x => x).ToList();
                        if (!permissions.SequenceEqual(basePermissions))
                        {
                            throw new UserFriendlyException("请选择相同操作权限的台账批量操作");
                        }
                    }
                }
            }

            // var ledgerDepartments = new List<LedgerDepartment>();
            // foreach (var ledgerId in input.LedgerIds)
            // {
            //     var ledgerDepartment = await _repository.FindAsync(x =>
            //         x.LedgerId == ledgerId && x.DepartmentId == CurrentUser.GetBigDepartmentId());
            //     if (ledgerDepartment != null)
            //     {
            //         ledgerDepartments.Add(ledgerDepartment);
            //     }
            // }
            //
            // // 假设第一个对象的Permissions作为基准  
            // if (ledgerDepartments.Count > 0)
            // {
            //     var basePermissions = ledgerDepartments[0].Permissions;
            //     if (basePermissions != null)
            //     {
            //         basePermissions = basePermissions.Distinct().OrderBy(x => x).ToList();
            //         // 检查其他对象的Permissions是否与基准一致  
            //         foreach (var item in ledgerDepartments)
            //         {
            //             var permissions = item.Permissions;
            //             if (permissions != null)
            //             {
            //                 permissions = permissions.Distinct().OrderBy(x => x).ToList();
            //                 if (!permissions.SequenceEqual(basePermissions))
            //                 {
            //                     throw new UserFriendlyException("请选择相同操作权限的台账批量操作");
            //                 }
            //             }
            //         }
            //     }

            //var basePermissions = ledgerDepartments[0].Permissions;
            //// 检查其他对象的Permissions是否与基准一致  
            //var departmentsWithInconsistentPermissions = ledgerDepartments
            //    .Skip(1) // 跳过第一个对象  
            //    .Where(x => basePermissions != null && x.Permissions != null &&
            //                !x.Permissions.SequenceEqual(basePermissions))
            //    .ToList();
            //if (departmentsWithInconsistentPermissions.Count > 0)
            //{
            //    throw new UserFriendlyException("请选择相同操作权限的台账批量操作");
            //}
        }
    }

    /// <summary>
    /// 批量新增台账跨部门授权
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    public async Task<List<string>> CreateLedgerDepartmentAsync(LedgerDepartmentDateAuthDto input)
    {
        var defaultList = new List<string>();
        //台账名称集合 用于消息通知
        var toDataLeaderIds = new List<PlatformUser>();
        var ledgerNames = new List<string>();
        var currentLedgerDepartments = await _repository.GetListAsync(x =>
            x.DepartmentId == CurrentUser.GetBigDepartmentId() && input.LedgerIds.Contains(x.LedgerId));
        foreach (var item in input.LedgerIds)
        {
            //获取传入的台账
            var ledger = await _ledgerCacheManager.GetLedgerCacheAsync(item);
            ledgerNames.Add(ledger.Name);
            // 获取当前部门被授权该台账信息
            var currentLedgerDepartment = currentLedgerDepartments.FirstOrDefault(x => x.LedgerId == item);
            // 如果当前部门没有被授权该台账，则忽略掉
            if (currentLedgerDepartment == null)
                throw new UserFriendlyException($"你所在的部门未被授权台账：{ledger.Name}");

            //获取已授权该台账的部门
            var authorizedLedgerDepartments = await _repository.GetListAsync(
                x => x.LedgerId == item &&
                     input.DepartmentIds.Contains(x.DepartmentId), false);
            foreach (var departmentId in input.DepartmentIds)
            {
                //获取传入的部门
                var department = await _departmentRepository.FindAsync(departmentId, false);

                var authorizedLedgerDepartment =
                    authorizedLedgerDepartments.FirstOrDefault(x => x.DepartmentId == departmentId);

                //若本部门不是其部门的授权人，则忽略
                if (authorizedLedgerDepartment != null &&
                    authorizedLedgerDepartment.AuthDepartmentId != CurrentUser.GetBigDepartmentId())
                    throw new UserFriendlyException($"你不是{department.Name}部门的授权部门，无法更改授权");

                //如果没有找到则新增一个跨部门授权
                if (authorizedLedgerDepartment == null)
                {
                    var ledgerDepartment = currentLedgerDepartment.AuthOtherDepartment(departmentId,
                        CurrentUser.GetId(), input.Permissions,
                        input.TableFieldIds, input.EditTableFieldIds, _ledgerOptions, ledger);
                    //计算三色灯状态
                    ledgerDepartment =
                        await _ledgerDepartmentManager.UpdateLitStatueByLedgerDepartment(ledgerDepartment,
                            ledger.ReminderConfig.Interval);
                    await _repository.InsertAsync(ledgerDepartment, true);

                    CurrentUnitOfWork.OnCompleted(async () =>
                    {
                        await _ledgerDepartmentManager.PublishLedgerFillUpdateStatisticsJob(ledgerDepartment.LedgerId,
                            ledgerDepartment.DepartmentId, ledgerDepartment.AuthDepartmentId);
                    });


                    //找数据领导
                    var users = await _departmentAppService.GetDepartmentExtendUsersListAsync(departmentId);
                    if (users.Items.Count < 1)
                    {
                        throw new UserFriendlyException($"请为{department.Name}中设置数据管理员");
                    }

                    foreach (var user in users.Items)
                    {
                        toDataLeaderIds.Add(new PlatformUser()
                        {
                            Id = user.Id,
                            Name = user.Name
                        });

                        var ledgerUser =
                            await _ledgerUserRepository.FindAsync(x =>
                                x.LedgerId == item
                                && x.UserId == user.Id
                                && x.DepartmentId == departmentId);
                        //判断该用户是否已存在该台账
                        if (ledgerUser == null)
                        {
                            //如果没有则新增
                            var ledgerUserNew = new LedgerUser(item, user.Id, departmentId)
                            {
                                Permissions = input.Permissions
                            };
                            await _ledgerUserRepository.InsertAsync(ledgerUserNew, true);
                        }
                        else
                        {
                            ledgerUser.Permissions = input.Permissions;
                            await _ledgerUserRepository.UpdateAsync(ledgerUser, true);
                        }
                    }

                    #region 增加授权模式 数据领导

                    var dataLeader =
                        await _roleRepository.FindAsync(x => x.Name == RoleSettings.DataLeader, false);
                    if (dataLeader == null)
                    {
                        throw new UserFriendlyException("数据领导角色不存在");
                    }

                    var authMode = await _ledgerPermissionsAuthorizationModeRepository.FindAsync(x =>
                        x.LedgerId == item &&
                        x.BigDepartmentId == departmentId &&
                        x.RoleId == dataLeader.Id &&
                        x.AuthorizationMode == LedgerAuthorizationModeType.Role);
                    if (authMode != null)
                    {
                        authMode.Permissions = input.Permissions;
                        await _ledgerPermissionsAuthorizationModeRepository.UpdateAsync(authMode, true);
                    }
                    else
                    {
                        var addAuthMode = new LedgerPermissionsAuthorizationMode
                        (
                            LedgerAuthorizationModeType.Role,
                            item,
                            departmentId,
                            input.Permissions
                        )
                        {
                            RoleId = dataLeader.Id,
                        };
                        await _ledgerPermissionsAuthorizationModeRepository.InsertAsync(addAuthMode);
                    }

                    #endregion
                }
                else
                {
                    string str;
                    if (authorizedLedgerDepartment.AuthDepartmentId == null)
                    {
                        authorizedLedgerDepartment.UserId = CurrentUser.GetId();
                        authorizedLedgerDepartment.AuthDepartmentId = CurrentUser.GetBigDepartmentId();
                        await _repository.UpdateAsync(authorizedLedgerDepartment);
                        str = $"'{ledger.Name}' 已重新授权给 '{department.Name}'，授权部门已添加";
                    }
                    else
                    {
                        str = $"'{ledger.Name}' 已授权给 '{department.Name}'，无需重复授权";
                    }

                    defaultList.Add(str);
                }
            }
        }

        if (!defaultList.Any())
        {
            defaultList.Add("新增授权成功");
            CurrentUnitOfWork.OnCompleted(async () =>
            {
                // await _distributedEventBus.PublishAsync(new LedgerCrossDepartmentEmpowerEto()
                // {
                //     FromUserId = CurrentUser.Id!.Value,
                //     LedgerNames = ledgerNames,
                //     LedgerIds = input.LedgerIds,
                //     ToDataLeaders = toDataLeaderIds,
                //     AuthDepartmentId = CurrentUser.GetBigDepartmentId()
                // });
                await _backgroundJobManager.EnqueueAsync(
                    new LedgerCrossDepartmentEmpowerArgs
                    {
                        FromUserId = CurrentUser.Id!.Value,
                        LedgerNames = ledgerNames,
                        LedgerIds = input.LedgerIds,
                        ToDataLeaders = toDataLeaderIds,
                        AuthDepartmentId = CurrentUser.GetBigDepartmentId()
                    });
            });

            return defaultList;
        }

        if (defaultList.Count < input.LedgerIds.Count * input.DepartmentIds.Count)
        {
            defaultList.Add("其余台账授权成功。");
            CurrentUnitOfWork.OnCompleted(async () =>
            {
                // await _distributedEventBus.PublishAsync(new LedgerCrossDepartmentEmpowerEto()
                // {
                //     FromUserId = CurrentUser.Id!.Value,
                //     LedgerNames = ledgerNames,
                //     LedgerIds = input.LedgerIds,
                //     ToDataLeaders = toDataLeaderIds,
                //     AuthDepartmentId = CurrentUser.GetBigDepartmentId()
                // });
                await _backgroundJobManager.EnqueueAsync(
                    new LedgerCrossDepartmentEmpowerArgs
                    {
                        FromUserId = CurrentUser.Id!.Value,
                        LedgerNames = ledgerNames,
                        LedgerIds = input.LedgerIds,
                        ToDataLeaders = toDataLeaderIds,
                        AuthDepartmentId = CurrentUser.GetBigDepartmentId()
                    });
            });
        }

        return defaultList;
    }

    /// <summary>
    /// 批量新增台账区县授权
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    public async Task<List<string>> CreateCountyLedgerDepartmentAsync(LedgerRegionDateAuthDto input)
    {
        var defaultList = new List<string>();
        foreach (var item in input.LedgerIds)
        {
            var ledgerOld = await _ledgerCacheManager.GetLedgerCacheAsync(item);
            foreach (var regionId in input.RegionIds)
            {
                //获取传入的区县
                var region = await _regionRepository.FindAsync(regionId, false);
                var countyLedgerAdmin =
                    await _roleRepository.FindAsync(x => x.Name == RoleSettings.CountyLedgerAdmin, false);
                if (region == null)
                {
                    throw new UserFriendlyException($"未查询到区域数据");
                }

                //找区县台账运维员
                DepartmentUsersByTreeCodeRequestDto dto = new DepartmentUsersByTreeCodeRequestDto
                {
                    TreeCode = region.TreeCode,
                    RoleId = countyLedgerAdmin.Id
                };
                var departmentUsers = await _departmentAppService.GetDepartmentUserListByTreeCodeAsync(dto);

                if (departmentUsers.Count < 1)
                {
                    throw new UserFriendlyException($"请为{region.Name}中设置区县台账运维员");
                }

                //departmentUsers = departmentUsers.DistinctBy(x => x.Department.DepartmentExtendId).ToList();
                foreach (var departmentUser in departmentUsers)
                {
                    var user = departmentUser;
                    var department = await _departmentRepository.FindAsync(user.Department.Id);
                    if (!department.DepartmentExtendId.HasValue)
                    {
                        throw new UserFriendlyException($"请为{department.Name}中设置大部门");
                    }

                    //查询是否有部门授权
                    var ledgerDepartment = await _repository.FindAsync(x =>
                        x.LedgerId == item && x.DepartmentId == department.DepartmentExtendId, false);
                    if (ledgerDepartment == null)
                    {
                        //如果没有授权则新增授权
                        var newLedgerDepartment = new LedgerDepartment(item, department.DepartmentExtendId.Value,
                            CurrentUser.GetId(),
                            CurrentUser.GetBigDepartmentId(), input.Permissions, input.TableFieldIds,
                            input.EditTableFieldIds, _ledgerOptions, ledgerOld)
                        {
                            County = true,
                            RegionName = region.Name
                        };
                        //计算三色灯状态
                        var ledger = await _ledgerRepository.GetAsync(item);
                        newLedgerDepartment =
                            await _ledgerDepartmentManager.UpdateLitStatueByLedgerDepartment(newLedgerDepartment,
                                ledger.ReminderConfig.Interval);
                        await _repository.InsertAsync(newLedgerDepartment, true);

                        CurrentUnitOfWork.OnCompleted(async () =>
                        {
                            await _ledgerDepartmentManager.PublishLedgerFillUpdateStatisticsJob(
                                newLedgerDepartment!.LedgerId,
                                newLedgerDepartment.DepartmentId, newLedgerDepartment.AuthDepartmentId);
                        });
                    }
                    else
                    {
                        //如果已经授权则提示
                        var str = $"'{ledgerOld.Name}' 已授权给 '{region.Name}'，无需重复授权";
                        defaultList.Add(str);
                    }

                    //判断是否授权人员
                    var ledgerUser = await _ledgerUserRepository.FirstOrDefaultAsync(x =>
                        x.LedgerId == item && x.UserId == user.Id &&
                        x.DepartmentId == department.DepartmentExtendId);
                    if (ledgerUser == null)
                    {
                        //如果没有则新增
                        var ledgerUserNew = new LedgerUser(item, user.Id, department.DepartmentExtendId.Value)
                        {
                            Permissions = input.Permissions
                        };
                        await _ledgerUserRepository.InsertAsync(ledgerUserNew, true);

                        #region 增加授权模式

                        var authMode = new LedgerPermissionsAuthorizationMode
                        (
                            LedgerAuthorizationModeType.User,
                            item,
                            department.DepartmentExtendId.Value,
                            ledgerUserNew.Permissions
                        )
                        {
                            UserId = user.Id,
                        };
                        await _ledgerPermissionsAuthorizationModeRepository.InsertAsync(authMode);

                        #endregion
                    }
                    else
                    {
                        ledgerUser.Permissions = input.Permissions;
                        await _ledgerUserRepository.UpdateAsync(ledgerUser, true);

                        #region 增加授权模式

                        var authMode = await _ledgerPermissionsAuthorizationModeRepository.FindAsync(x =>
                            x.LedgerId == item &&
                            x.BigDepartmentId == ledgerUser.DepartmentId &&
                            x.UserId == user.Id &&
                            x.AuthorizationMode == LedgerAuthorizationModeType.User);
                        if (authMode != null)
                        {
                            ledgerUser.Permissions = input.Permissions
                                .Where(p => p != LedgerPermissionType.Approve)
                                .ToList();
                            await _ledgerPermissionsAuthorizationModeRepository.UpdateAsync(authMode, true);
                        }
                        else
                        {
                            var addAuthMode = new LedgerPermissionsAuthorizationMode
                            (
                                LedgerAuthorizationModeType.User,
                                item,
                                department.DepartmentExtendId.Value,
                                ledgerUser.Permissions
                            )
                            {
                                UserId = user.Id,
                            };
                            await _ledgerPermissionsAuthorizationModeRepository.InsertAsync(addAuthMode, true);
                        }

                        #endregion
                    }
                }
            }
        }

        if (!defaultList.Any())
        {
            defaultList.Add("新增授权成功");
            return defaultList;
        }

        if (defaultList.Count < input.LedgerIds.Count * input.RegionIds.Count)
        {
            defaultList.Add("其余台账授权成功。");
        }

        return defaultList;
    }


    /// <summary>
    /// 响应
    /// </summary>
    /// <param name="id"></param>
    /// <param name="departmentId"></param>
    public async Task DepartmentResponse(Guid id, Guid departmentId)
    {
        var ledgerDepartment = await _repository.GetAsync(x => x.LedgerId == id && x.DepartmentId == departmentId);
        if (ledgerDepartment != null)
        {
            if (ledgerDepartment.County == true)
            {
                var countyLedgerDepartments = await _repository.GetListAsync(x =>
                    x.LedgerId == id && x.RegionName == ledgerDepartment.RegionName);
                foreach (var item in countyLedgerDepartments)
                {
                    item.Response = true;
                    await _repository.UpdateAsync(item);
                }
            }
            else
            {
                ledgerDepartment.Response = true;
                await _repository.UpdateAsync(ledgerDepartment);
            }
        }
    }

    /// <summary>
    /// 批量响应
    /// </summary>
    /// <param name="input"></param>
    public async Task BatchResponse(DepartmentResponseDto input)
    {
        foreach (var ledgerId in input.LedgerIds)
        {
            var ledgerDepartment = await _repository.GetAsync(x =>
                x.LedgerId == ledgerId && x.DepartmentId == CurrentUser.GetBigDepartmentId());
            if (ledgerDepartment == null) continue;
            {
                if (ledgerDepartment.County == true)
                {
                    var countyLedgerDepartments = await _repository.GetListAsync(x =>
                        x.LedgerId == ledgerId && x.RegionName == ledgerDepartment.RegionName);
                    foreach (var item in countyLedgerDepartments)
                    {
                        item.Response = true;
                        await _repository.UpdateAsync(item);
                    }
                }
                else
                {
                    ledgerDepartment.Response = true;
                    await _repository.UpdateAsync(ledgerDepartment);
                }
            }
        }
    }

    /// <summary>
    /// 发送提醒信息
    /// </summary>
    /// <param name="ledgerId"></param>
    /// <param name="departmentId"></param>
    public async Task SendRemindResponse(Guid ledgerId, Guid departmentId)
    {
        var ledgerDepartment =
            await _repository.GetAsync(x => x.LedgerId == ledgerId && x.DepartmentId == departmentId, false);

        if (ledgerDepartment.County == true)
        {
            var countyLedgerDepartments = await _repository.GetListAsync(x =>
                x.LedgerId == ledgerId && x.RegionName == ledgerDepartment.RegionName);
            // await _distributedEventBus.PublishAsync(new SendEmpowerMessagesEto()
            // {
            //     DepartmentId = departmentId,
            //     CountyLedgerDepartmentIds = countyLedgerDepartments.Select(s => s.DepartmentId).ToList(),
            //     LedgerId = ledgerId,
            //     AuthDepartmentId = ledgerDepartment.AuthDepartmentId
            // });
            await _backgroundJobManager.EnqueueAsync(new LedgerCountyEmpowerArgs
            {
                DepartmentId = departmentId,
                CountyLedgerDepartmentIds = countyLedgerDepartments.Select(s => s.DepartmentId).ToList(),
                LedgerId = ledgerId,
                AuthDepartmentId = ledgerDepartment.AuthDepartmentId
            });
        }
        else
        {
            var ledgerUser =
                await _ledgerUserRepository.FirstOrDefaultAsync(s =>
                    s.LedgerId == ledgerId && s.DepartmentId == departmentId);
            var user = await _identityUserRepository.GetAsync(s => s.Id == ledgerUser.UserId, false);
            var ledger = await _ledgerRepository.GetAsync(ledgerId, false);

            var authDepartment =
                await _departmentRepository.FirstOrDefaultAsync(s => s.Id == ledgerDepartment.AuthDepartmentId);

            await _distributedEventBus.PublishAsync(new SendEmpowerMessageEto()
            {
                Departmentid = departmentId,
                UserId = user.Id,
                UserName = user.UserName,
                LedgerName = ledger.Name,
                AuthDepartmentName = authDepartment?.Name
            });
        }
    }

    public async Task<string> BatchSendRemindResponse(BatchSendRemindDto input)
    {
        // 获取台账信息
        var ledger = await _ledgerRepository.GetAsync(input.LedgerId, false);

        // 提取部门ID和授权部门ID
        var departmentIds = input.BatchSendRemindDepartments.Select(s => s.DepartmentId).ToList();
        var authDepartmentIds = input.BatchSendRemindDepartments.Select(s => s.AuthDepartmentId).ToList();

        if (!departmentIds.Any())
        {
            // 如果没有部门ID，直接返回
            throw new UserFriendlyException("请勾选部门！");
        }

        // 查询未响应的台账部门
        var ledgerDepartments = await _repository.GetListAsync(x =>
            x.LedgerId == input.LedgerId &&
            departmentIds.Contains(x.DepartmentId) &&
            x.Response != true);

        if (!ledgerDepartments.Any())
        {
            throw new UserFriendlyException("未查询到未签收的部门！");
        }

        var authDepartments = await _departmentRepository.GetListAsync(x => authDepartmentIds.Contains(x.Id));
        var recipients = "";
        foreach (var ledgerDepartment in ledgerDepartments)
        {
            var authDepartment = authDepartments.FirstOrDefault(s => s.Id == ledgerDepartment.AuthDepartmentId);

            await _backgroundJobManager.EnqueueAsync(new BatchSendRemindMessageArgs()
            {
                DepartmentId = ledgerDepartment.DepartmentId,
                CreatorUserId = CurrentUser.GetId(),
                CreatorUserName = CurrentUser.UserName,
                LedgerName = ledger.Name,
                AuthDepartmentName = authDepartment?.Name,
                NotifyLedgerTypes = input.NotifyLedgerTypes
            });

            var extendUsers =
                await _departmentAppService.GetDepartmentExtendUsersListAsync(ledgerDepartment.DepartmentId);
            var toUsers = extendUsers?.Items?.Select(x => new UserIdentifier(x.Id, x.UserName)).ToList();
            if (toUsers != null)
            {
                foreach (var toUser in toUsers)
                {
                    recipients += $"{toUser.UserName},";
                }
            }
        }

        return $"已发送提醒给：{recipients}";
    }

    /// <summary>
    /// 根据TreeCode
    /// </summary>
    /// <param name="regionCode"></param>
    /// <returns></returns>
    public async Task<Dictionary<Guid, string>> GetDepartmentAuthorizedByRegionCodeView(string regionCode)
    {
        var authorized =
            await _ledgerDepartmentDapperRepository.GetDepartmentAuthorizedByRegionCodeView(regionCode);
        return authorized.GroupBy(f => f.LedgerId)
            .Select(f => new KeyValuePair<Guid, string>(f.Key, f.FirstOrDefault()?.LedgerName))
            .ToDictionary(f => f.Key, f => f.Value);
    }

    /// <summary>
    /// 获取区域授权部门
    /// </summary>
    /// <returns></returns>
    public async Task<List<DepartmentSmallDto>> GetAuthDepartmentByRegionAsync(Guid regionId)
    {
        var departmentSmalls = await _ledgerDepartmentDapperRepository
            .GetAuthDepartmentByRegionAsync(regionId, CurrentUser.GetBigDepartmentId());

        return ObjectMapper.Map<List<DepartmentSmallView>, List<DepartmentSmallDto>>(departmentSmalls);
    }

    /// <summary>
    /// 获取区域授权部门
    /// </summary>
    /// <returns></returns>
    public async Task<List<DepartmentSmallDto>> GetChildAuthDepartmentByRegionAsync(Guid regionId)
    {
        var departmentSmalls = await _ledgerDepartmentDapperRepository
            .GetChildAuthDepartmentByRegionAsync(regionId, CurrentUser.GetBigDepartmentId());

        return ObjectMapper.Map<List<DepartmentSmallView>, List<DepartmentSmallDto>>(departmentSmalls);
    }

    /// <summary>
    /// 根据ledgerId查询授权信息
    /// </summary>
    /// <param name="ledgerId"></param>
    /// <returns></returns>
    public async Task<List<LedgerDepartmentDto>> GetDepartmentAuthorizedByLedgerId(Guid ledgerId)
    {
        var departments = await _repository.GetListAsync(x => x.LedgerId == ledgerId);
        return ObjectMapper.Map<List<LedgerDepartment>, List<LedgerDepartmentDto>>(departments);
    }


    /// <summary>
    /// 根据ledgerId查询自己的授权信息
    /// </summary>
    /// <param name="ledgerId"></param>
    /// <returns></returns>
    public async Task<LedgerDepartmentDto> GetAutoDepartmentByLedgerId(Guid ledgerId)
    {
        var department = await _repository.FirstOrDefaultAsync(x =>
            x.LedgerId == ledgerId && x.DepartmentId == CurrentUser.GetBigDepartmentId());
        return ObjectMapper.Map<LedgerDepartment, LedgerDepartmentDto>(department);
    }


    /// <summary>
    /// 获取向下的授权链部门Id
    /// </summary>
    /// <param name="ledgerId"></param>
    /// <param name="largeDepartmentId"></param>
    /// <returns></returns>
    public Task<List<Guid>> GetAuthorizationChainDepartmentIdByDown(Guid ledgerId, Guid largeDepartmentId)
    {
        return _ledgerDepartmentManager.GetAuthorizationChainDepartmentIdByDown(ledgerId, largeDepartmentId);
    }

    /// <summary>
    /// 获取当前部门是否是管理部门
    /// </summary>
    /// <param name="ledgerId"></param>
    /// <param name="largeDepartmentId"></param>
    /// <returns></returns>
    public async Task<bool> IsLedgerManageDepartment(Guid ledgerId, Guid largeDepartmentId)
    {
        var ledgerDepartmentCache = await _ledgerDepartmentCacheManage.GetCacheItemAsync(ledgerId, largeDepartmentId);
        return ledgerDepartmentCache?.ManageLedgerDepartment ?? false;
    }

    /// <summary>
    /// 设置台账的填报状态
    /// </summary>
    /// <param name="input"></param>
    public async Task SetFillProgressStatus(UpdateLedgerDepartmentFillStatueDto input)
    {
        await _ledgerDepartmentManager.SetFillProgressStatus(input.LedgerId, input.Statue);
    }

    /// <summary>
    /// 手动触发更新亮灯状态
    /// </summary>
    public async Task UpdateLitStatueDistribute(Guid? ledgerId)
    {
        if (!CurrentUser.IsAdminOrLedgerAdmin())
        {
            throw new UserFriendlyException("无操作权限"); 
        }
        await _ledgerDepartmentManager.UpdateLitStatueDistribute(ledgerId);
    }

    /// <summary>
    /// 手动触发更新单条/全部台账部门填报统计数据
    /// </summary>
    /// <param name="input"></param>
    public async Task LedgerDepartmentStatistics(LedgerDepartmentStatisticsRequestDto input)
    {
        Logger.LogInformation($"LedgerDepartmentStatistics***出发手动修复***{input.LedgerId}***{input.TaskItemId}");
        if (!CurrentUser.IsAdmin())
            throw new UserFriendlyException("权限不足");

        if (input.LedgerId is null)
            await _ledgerDepartmentManager.LedgerDepartmentStatisticsDistribute();
        else
            // await _ledgerDepartmentManager.LedgerDepartmentStatisticsAsync(ledgerId!.Value);
            await _backgroundJobManager.EnqueueAsync(new LedgerDepartmentStatisticsArgs()
            {
                LedgerId = input.LedgerId!.Value,
                TaskItemId = input.TaskItemId ?? null
            });
    }

    /// <summary>
    /// 修复台账部门授权部门
    /// </summary>
    public async Task RepairLedgerDepartmentAuthDepartment(List<RepairLedgerDepartmentAuthDepartmentDto> input)
    {
        if (input == null || !input.Any())
        {
            throw new UserFriendlyException("输入参数不能为空");
        }

        if (!CurrentUser.IsAdministrators())
        {
            throw new UserFriendlyException("您没有权限");
        }

        using var uow = _unitOfWorkManager.Begin(requiresNew: true);
        try
        {
            // 先获取所有相关的 LedgerId
            var ledgerIds = input.Select(x => x.LedgerId).Distinct().ToList();
            var departmentIds = input.Select(x => x.DepartmentId).Distinct().ToList();


            // 分步查询
            var ledgerDepartments = await _repository.GetListAsync(x =>
                ledgerIds.Contains(x.LedgerId) &&
                departmentIds.Contains(x.DepartmentId));

            // 在内存中进行精确匹配
            ledgerDepartments = ledgerDepartments.Where(x =>
                input.Any(i =>
                    i.LedgerId == x.LedgerId &&
                    i.DepartmentId == x.DepartmentId)).ToList();

            foreach (var item in input)
            {
                var ledgerDepartment = ledgerDepartments.FirstOrDefault(x =>
                    x.LedgerId == item.LedgerId &&
                    x.DepartmentId == item.DepartmentId &&
                    x.AuthDepartmentId == item.AuthDepartmentId);

                if (ledgerDepartment != null)
                {
                    ledgerDepartment.AuthDepartmentId = item.RepairAuthDepartmentId;
                    Logger.LogInformation($"修复台账部门授权成功: LedgerId={item.LedgerId}, DepartmentId={item.DepartmentId}");
                    await _repository.UpdateAsync(ledgerDepartment);
                }
            }

            await uow.CompleteAsync();
        }
        catch (Exception e)
        {
            await uow.RollbackAsync();
            Logger.LogError(e, "修复台账部门授权部门失败");
            throw new UserFriendlyException("修复台账部门授权部门失败");
        }
    }


    /// <summary>
    /// 修复台账部门县区
    /// </summary>
    /// <param name="input"></param>
    public async Task RepairLedgerDepartmentCounty(RepairLedgerDepartmentCountyDto input)
    {
        if (input == null)
        {
            throw new UserFriendlyException("输入参数不能为空");
        }


        if (!CurrentUser.IsAdministrators())
        {
            throw new UserFriendlyException("您没有权限");
        }

        var ledgerDepartments = await _repository.GetListAsync(x =>
            input.LedgerIds.Contains(x.LedgerId) && x.DepartmentId == input.DepartmentId &&
            x.RegionName == input.RegionName && x.County == true);
        foreach (var ledgerDepartment in ledgerDepartments)
        {
            //移除授权模式
            var authMode = await _ledgerPermissionsAuthorizationModeRepository
                .GetListAsync(x =>
                    x.BigDepartmentId == ledgerDepartment.DepartmentId && x.LedgerId == ledgerDepartment.LedgerId &&
                    x.AuthorizationMode == LedgerAuthorizationModeType.Role);
            foreach (var item in authMode)
            {
                await _ledgerPermissionsAuthorizationModeRepository.HardDeleteAsync(item);
            }
        }

        //查询ledgeruser表
        var ledgerUsers = await _ledgerUserRepository.GetListAsync(x =>
            input.LedgerIds.Contains(x.LedgerId) && x.DepartmentId == input.DepartmentId);
        foreach (var ledgerUser in ledgerUsers)
        {
            //移除授权模式

            var authMode = await _ledgerPermissionsAuthorizationModeRepository
                .GetListAsync(x =>
                    x.UserId == ledgerUser.UserId && x.LedgerId == ledgerUser.LedgerId &&
                    x.BigDepartmentId == ledgerUser.DepartmentId);

            foreach (var item in authMode)
            {
                await _ledgerPermissionsAuthorizationModeRepository.HardDeleteAsync(item);
            }
        }

        await _repository.DeleteManyAsync(ledgerDepartments);
        await _ledgerUserRepository.DeleteManyAsync(ledgerUsers);
    }

    /// <summary>
    /// 导出  当前页面统计数据
    /// </summary>
    /// <returns></returns>
    public async Task<byte[]> ExportLedgerDepartmentFillStatistics(ExportLedgerFillStatisticsDto input)
    {
        try
        {
            var groupedQueryable = await GetQueryable(input.Ids.ToList());

            var data = await AsyncExecuter.ToListAsync(groupedQueryable);
            if (data.Count == 0)
                throw new UserFriendlyException("没有统计数据");

            var list = new List<LedgerDepartmentStatisticsItemDto>();
            //导出数据
            List<IDictionary<string, object>> dataDices = new();
            _ledgerDepartmentManager.GenerateDepartmentFillHeaders(data, list, dataDices);
            var convertHeader = await _ledgerDepartmentManager.GenerateHeadersAsync(list.FirstOrDefault()!
                .LedgerDepartmentStatisticsItems.OrderBy(x => x.Sort).ToList());
            var headers = ExcelHeader.ConvertToExcelHeaders(convertHeader);
            var path = _excelExporter.Export(dataDices, headers,
                CurrentUser.Id + "_" + CurrentUser.GetBigDepartmentId() + ".xlsx");
            using var workbook = new XSSFWorkbook(path);

            using var stream = new MemoryStream();
            // 将工作簿写入文件  
            workbook.Write(stream, true);
            workbook.Close();
            return await stream.GetAllBytesAsync();
        }
        catch (Exception e)
        {
            Logger.LogError($"{CurrentUser.Id} 导出  当前页面填报统计数据出现异常：{e.Message}");
            throw;
        }
    }

    #region 导出  各业务表当前周期详细更新数据

    private async Task<List<LedgerDepartmentDataDetailStatistics>> GetDetailQueryable(
        [CanBeNull] List<Guid> ledgerIds)
    {
        // 如果ledgerIds为null，返回空列表
        if (ledgerIds == null || !ledgerIds.Any())
        {
            Logger.LogInformation("传入的 ledgerIds 为null");
            return new List<LedgerDepartmentDataDetailStatistics>();
        }
        
        // 通过ledgerids 查询出 taskItemIds
        var latestTaskItemIds =
            (await _wfTaskManager.GetLatestTaskItemIdsByBindObjectIds(ledgerIds.Select(x => x.ToString()).ToList()))
            .Where(id => id.HasValue)
            .Select(id => id.Value)
            .ToList();
        var roles = CurrentUser.FindBusinessRole();
        var baseRoles = CurrentUser.Roles;
        var bigDepartmentId = CurrentUser.GetBigDepartmentId();
        var departmentIds = new List<Guid>();
        LedgerDepartmentFillStatisticsType type = LedgerDepartmentFillStatisticsType.Mine;
        // 应用角色过滤条件
        if (CurrentUser.IsAdminOrLedgerAdmin())
        {
            type = LedgerDepartmentFillStatisticsType.City;
        }
        else if (CurrentUser.IsCountyLedgerAdmin())
        {
            var region =
                await _userAppService.GetUserRegionAsync(CurrentUser.GetId(), CurrentUser.GetDepartmentId());
            if (region.Grade != RegionGrade.District)
                throw new UserFriendlyException("区县运维管理员所在部门未配置在区县下，请联系管理员");

            var regions = (await _regionRepository.WithDetailsAsync())
                .Where(x => x.TreeCode.StartsWith(region.TreeCode))
                .OrderBy(x => x.TreeCode)
                .ToList();

            var regionIds = regions.Select(x => x.Id).ToList();
            departmentIds = (await _departmentAppService.GetDepartmentExtendIdsByRegionsAsync(regionIds))
                .Where(id => id.HasValue)
                .Select(id => id.Value)
                .ToList();
            // departmentIds = departmentDto.Select(x => x.Id).ToList();
            type = LedgerDepartmentFillStatisticsType.Counties;
        }

        var data = await _ledgerDepartmentDataDetailStatisticsDapperRepository.GetFillDataAsync(ledgerIds, type,
            bigDepartmentId, departmentIds, latestTaskItemIds);
        return data;
    }

    //
    // private async Task<IQueryable<LedgerDepartmentDataDetailStatistics>> GetDetailQueryable([CanBeNull] List<Guid> ledgerIds)
    // {
    //     var queryable = await _ledgerDepartmentDataDetailStatisticsRepository.WithDetailsAsync();
    //     queryable = queryable.WhereIf(ledgerIds!.Any(), x => ledgerIds.Contains(x.LedgerId))
    //         .Where(x => x.Ledger.IsOnline == true && x.Ledger.ReminderConfig.Interval > 0);
    //     var roles = CurrentUser.FindBusinessRole();
    //     var baseRoles = CurrentUser.Roles;
    //
    //     // 应用角色过滤条件
    //     if (roles.Contains(RoleSettings.Admin) ||
    //         baseRoles.Contains(RoleSettings.Admin) ||
    //         roles.Contains(RoleSettings.LedgerAdmin) ||
    //         baseRoles.Contains(RoleSettings.LedgerAdmin))
    //     {
    //     }
    //     else if ((roles.Contains(RoleSettings.CountyLedgerAdmin) ||
    //               baseRoles.Contains(RoleSettings.CountyLedgerAdmin)))
    //     {
    //         Logger.LogInformation(("CountyLedgerAdmin管理员获取台账部门统计数据"));
    //         var regionDto =
    //             await _userAppService.GetUserRegionAsync(CurrentUser.GetId(), CurrentUser.GetDepartmentId());
    //         if (regionDto.Grade != RegionGrade.District)
    //             throw new UserFriendlyException("区县运维管理员所在部门未配置在区县下，请联系管理员");
    //
    //         var regions = (await _regionRepository.WithDetailsAsync())
    //             .Where(x => x.TreeCode.StartsWith(regionDto.TreeCode)).OrderBy(x => x.TreeCode).ToList();
    //         var departmentDos =
    //             await _departmentAppService.GetDepartmentListAsync(regions.Select(x => x.Id).ToList());
    //         var ids = departmentDos.Select(x => x.Id).ToList();
    //
    //         queryable = queryable.Include(x => x.Ledger)
    //             .ThenInclude(x => x.LedgerDepartments)
    //             .WhereIf(ids.Any(), x => ids.Contains(x.DepartmentId));
    //     }
    //     else
    //     {
    //         var userId = CurrentUser.GetId();
    //         var bigDepartmentId = CurrentUser.GetBigDepartmentId();
    //         queryable = queryable
    //             .Include((x => x.Ledger))
    //             .ThenInclude((x => x.LedgerUsers))
    //             .Where(x => x.Ledger.LedgerUsers.Any(u => u.UserId == userId && u.DepartmentId == bigDepartmentId))
    //             .Where(x => x.DepartmentId == bigDepartmentId || x.AuthDepartmentId == bigDepartmentId);
    //     }
    //
    //     return queryable;
    // }

    public async Task<byte[]> ExportLedgerDepartmentDataStatistics(LedgerFillStatisticsExportDto input)
    {
        var currentProcessingLedgerId = Guid.Empty;
        var ledgerCount = 0;
        const int sheetsPerWorkbook = 200; // 每个工作簿的工作表数量
        var errorMessage = "";
        try
        {
            var dataQueryable = await GetDetailQueryable(input.LedgerIds);

            // 获取总记录数
            var totalCount = dataQueryable.Count;
            // var totalCount = await dataQueryable.CountAsync();
            Logger.LogInformation($"记录总数{totalCount}_");
            errorMessage = $"记录总数{totalCount}_";
            if (totalCount == 0)
                return await Task.FromResult(Encoding.UTF8.GetBytes("没有统计数据"));

            using var zipStream = new MemoryStream();
            using (var archive = new ZipArchive(zipStream, ZipArchiveMode.Create, true))
            {
                int fileCount = 0;
                var workbook = new XSSFWorkbook();
                int currentSheetCount = 0;
                //后续优化成sql 只查询需要的数据视图
                List<LedgerDepartmentDataDetailStatistics> details = dataQueryable;
                // List<LedgerDepartmentDataDetailStatistics> details = await AsyncExecuter.ToListAsync(dataQueryable);
                var batchGroups = details.GroupBy(f => f.LedgerId).ToList();
                ledgerCount = batchGroups.Count();

                errorMessage = $"分组后台账总数{ledgerCount}_";

                for (int offset = 0; offset < batchGroups.Count; offset += sheetsPerWorkbook)
                {
                    int sheetNumber = 0;
                    var batchGroup = batchGroups.Skip(offset).Take(sheetsPerWorkbook);
                    foreach (var group in batchGroup)
                    {
                        currentProcessingLedgerId = group.Key;
                        var ledgerIdGroup = group.ToList();

                        var list = new List<LedgerDepartmentStatisticsItemDto>();
                        _ledgerDepartmentManager.GenerateDetailHeaders(ledgerIdGroup, list);
                        var dataDices = _ledgerDepartmentManager.GetDataDices(ledgerIdGroup);

                        var convertHeader = await _ledgerDepartmentManager.GenerateHeadersAsync(
                            list.FirstOrDefault()?.LedgerDepartmentStatisticsItems.OrderBy(x => x.Sort).ToList());
                        var headers = ExcelHeader.ConvertToExcelHeaders(convertHeader);
                        sheetNumber++;
                        var sheetName = GenerateUniqueSheetName(group.FirstOrDefault()!.Ledger.Name, sheetNumber);
                        var sheet = workbook.CreateSheet(sheetName);

                        // 创建表头
                        CreateHeader(sheet, headers);

                        // 填充数据
                        FillSheetData(sheet, dataDices, headers);

                        currentSheetCount++;

                        // 检查是否需要创建新的工作簿
                        if (currentSheetCount >= sheetsPerWorkbook)
                        {
                            await SaveWorkbookToZip(workbook, archive, ++fileCount);
                            workbook = new XSSFWorkbook();
                            currentSheetCount = 0;
                        }
                    }
                }


                // 保存最后一个工作簿
                if (currentSheetCount > 0)
                {
                    await SaveWorkbookToZip(workbook, archive, ++fileCount);
                }
            }

            return zipStream.ToArray();
        }
        catch (Exception e)
        {
            Logger.LogError(
                $"{CurrentUser.Id} 导出各业务表当前周期详细更新数据出现异常：{e.Message}，异常台账_{currentProcessingLedgerId},总台账个数_{ledgerCount},记录信息_{errorMessage}");
            throw;
        }
    }

    private void CreateHeader(ISheet sheet, List<ExcelHeader> headers)
    {
        var headerRow = sheet.CreateRow(0);
        for (int i = 0; i < headers.Count; i++)
        {
            headerRow.CreateCell(i).SetCellValue(headers[i].AliasName);
        }
    }

    private void FillSheetData(ISheet sheet, List<IDictionary<string, object>> dataDices, List<ExcelHeader> headers)
    {
        for (int i = 0; i < dataDices.Count; i++)
        {
            var row = sheet.CreateRow(i + 1);
            var dataDict = dataDices[i];
            for (int j = 0; j < headers.Count; j++)
            {
                row.CreateCell(j).SetCellValue(dataDict[headers[j].Name]?.ToString() ?? string.Empty);
            }
        }
    }

    private async Task SaveWorkbookToZip(XSSFWorkbook workbook, ZipArchive archive, int fileCount)
    {
        var fileName = $"导出台账填报统计数据_{fileCount}_{Guid.NewGuid()}.xlsx";
        var fileEntry = archive.CreateEntry(fileName);
        await using (var entryStream = fileEntry.Open())
        {
            workbook.Write(entryStream);
        }
    }

    #endregion

    private static string GenerateUniqueSheetName(string originalSheetName, int sheetNumber)
    {
        // 移除特殊字符
        string cleanedName = new string(originalSheetName.Where(c => char.IsLetterOrDigit(c) || c == '_').ToArray());

        // 生成唯一标识符
        string uniqueIdentifier = Guid.NewGuid().ToString("N").Substring(0, 8); // 取 GUID 的前 8 个字符

        // 拼接处理后的名称和唯一标识符
        string combinedName = $"{sheetNumber}_{cleanedName}_{uniqueIdentifier}";

        // 截断以满足长度限制
        if (combinedName.Length > 30)
        {
            combinedName = combinedName.Substring(0, 30);
        }

        return combinedName;
    }

    private async Task<IQueryable<LedgerDepartmentDataStatistics>> GetQueryable([CanBeNull] List<Guid> ledgerIds)
    {
        // 如果ledgerIds为null，返回空列表
        if (ledgerIds == null || !ledgerIds.Any())
        {
            Logger.LogInformation("传入的 ledgerIds 为null");
            throw new UserFriendlyException("传入的业务表 未找到统计数据");
        }

        // var roles = CurrentUser.FindBusinessRole();
        // var baseRoles = CurrentUser.Roles;

        // 由于需求改为需要历史周期，当下载台账统计列表 当前页面 时需要添加最新周期的任务项Id
        var latestTaskItemIds =
            await _wfTaskManager.GetLatestTaskItemIdsByBindObjectIds(ledgerIds!.Select(x => x.ToString()).ToList());

        // 初始查询
        var queryable = await _ledgerDepartmentDataDetailStatisticsRepository.WithDetailsAsync();
        queryable = queryable.WhereIf(ledgerIds!.Any(), x => ledgerIds.Contains(x.LedgerId))
            .Where(x => latestTaskItemIds.Contains(x.LatestTaskItemId));

        if (CurrentUser.IsAdminOrLedgerAdmin())
        {
            // 不需要额外的过滤条件
        }
        else if (CurrentUser.IsCountyLedgerAdmin())
        {
            var region =
                await _userService.GetUserRegionAsync(CurrentUser.GetId(), CurrentUser.GetDepartmentId());
            if (region.Grade != RegionGrade.District)
                throw new UserFriendlyException("区县运维管理员所在部门未配置在区县下，请联系管理员");

            var regions = (await _regionRepository.WithDetailsAsync())
                .Where(x => x.TreeCode.StartsWith(region.TreeCode))
                .OrderBy(x => x.TreeCode)
                .ToList();

            var regionIds = regions.Select(x => x.Id).ToList();
            var departmentIds = await _departmentAppService.GetDepartmentExtendIdsByRegionsAsync(regionIds);

            queryable = queryable
                .WhereIf(departmentIds.Any(),
                    x => departmentIds.Contains(x.DepartmentId));

            // ||departmentIds.Contains((Guid)x.AuthDepartmentId)
        }
        else //工作人员
        {
            var bigDepartmentId = CurrentUser.GetBigDepartmentId();
            queryable = queryable
                .Where(x => x.AuthDepartmentId == bigDepartmentId || x.DepartmentId == bigDepartmentId);
        }

        var groupedQueryable = queryable
            .AsEnumerable()
            .GroupBy(x => x.LedgerId)
            .Select(g => new LedgerDepartmentDataStatistics
            {
                LedgerId = g.Key,
                Ledger = g.Select(x => x.Ledger).FirstOrDefault(),
                DepartmentCountByNeedUpdate = g.Count(x => x.LedgerId == g.Key),
                DepartmentCountByUpdated = g.Count(x =>
                    x.FillProgressStatus == FillProgressStatus.Completed ||
                    x.FillProgressStatus == FillProgressStatus.NoNeedUpdate),
                DepartmentCountByNotUpdated = g.Count() -
                                              g.Count(x =>
                                                  x.FillProgressStatus == FillProgressStatus.Completed ||
                                                  x.FillProgressStatus == FillProgressStatus.NoNeedUpdate),
                StatisticsTime = g.Select(x => x.StatisticsTime).FirstOrDefault(),
                StatisticsUpdateTime = g.Select(x => x.StatisticsUpdateTime).FirstOrDefault()
            });

        return groupedQueryable.AsQueryable();
    }
}