using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Dapper;
using Inspur.Abp.Ledger.Core;
using Inspur.Abp.Ledger.EntityFrameworkCore;
using Inspur.Abp.Platform.Organizational;
using JetBrains.Annotations;
using Volo.Abp.DependencyInjection;
using Volo.Abp.EntityFrameworkCore;

namespace Inspur.Abp.Ledger.OceanBase.Dapper.Repository.Core
{
    public class LedgerDepartmentDataDetailStatisticsDapperRepository : LedgerDepartmentDataDetailStatisticsDapperRepositoryBase
    {
        public LedgerDepartmentDataDetailStatisticsDapperRepository(IDbContextProvider<ILedgerDbContext> dbContextProvider) :
            base(dbContextProvider)
        {
        }

        public override async Task<(List<LedgerDepartmentDataDetailStatisticsView> Data, int TotalCount)>
            GetDetailAndCountByFilterAsync(
                Guid ledgerId,
                RegionGrade? grade, [CanBeNull] string filter, FillProgressStatus? fillProgressStatus,
                [CanBeNull] string districtId, int skip, int take, LedgerDepartmentFillStatisticsType type,
                Guid bigDepartmentId, List<Guid?> departmentIds, Guid? taskItemId)
        {
            // 构建数据查询 SQL
            var sqlBuilder = new StringBuilder();
            // 构建计数查询 SQL
            var countSqlBuilder = new StringBuilder();

            sqlBuilder.AppendLine(@"
                SELECT
                    detail.*,
                    CASE 
                        WHEN region.`Grade` IN (2, 3) THEN region.`Name`
                        WHEN region.`Grade` IN (4, 5, 6) THEN parent_region.`Name`
                    END AS `BelongRegionName`,
                    -- Department字段放在最后
                    depart.`Id`,  -- 这里是分割点
                    depart.`Name`
                    ,task.`Id`,
                    task.`Name`,
                    task.`Deadline`
                FROM
                    `Ledger_LedgerDepartmentDataDetailStatistics` AS detail
                    LEFT JOIN `Platform_Departments` AS depart ON detail.`DepartmentId` = depart.`Id`
                    LEFT JOIN `Platform_Regions` AS region ON depart.`RegionId` = region.`Id`
                    LEFT JOIN `Platform_Regions` AS parent_region ON region.`ParentId` = parent_region.`Id`
                    INNER JOIN `Platform_WFTaskItems` AS task ON detail.`LatestTaskItemId` = task.`Id`
                    inner join `Ledger_LedgerDepartments` lld on (lld.`DepartmentId` = depart.`Id` and detail.`LedgerId`  = lld.`LedgerId`)
                WHERE
                    detail.`LedgerId` = @LedgerId
                    AND depart.`IsDeleted` = FALSE
                    AND region.`IsDeleted` = FALSE
                    AND detail.`LatestTaskItemId` is not null
                    AND task.`IsDeleted` = FALSE
                    ");

            // 如果grade不为空，添加grade条件
            if (grade.HasValue)
            {
                sqlBuilder.AppendLine(@" AND region.`Grade` = @Grade");
            }

            // 添加共同的条件
            if (!string.IsNullOrEmpty(filter))
            {
                sqlBuilder.AppendLine(@" AND depart.`Name` LIKE LOWER(@Filter)");
            }

            if (fillProgressStatus != null)
            {
                sqlBuilder.AppendLine(@" AND detail.`FillProgressStatus` = @FillProgressStatus");
            }

            if (!string.IsNullOrEmpty(districtId))
            {
                sqlBuilder.AppendLine(@" AND region.`Id` IN @RegionIds");
            }

            if (type == LedgerDepartmentFillStatisticsType.Mine)
            {
                sqlBuilder.AppendLine(
                    @" AND (detail.`DepartmentId` = @DepartmentId OR detail.`AuthDepartmentId` = @DepartmentId)");
            }
            else if (type == LedgerDepartmentFillStatisticsType.Counties)
            {
                sqlBuilder.AppendLine(@" AND detail.`DepartmentId` IN @DepartmentIds");
            }

            // 如果taskItemId不为空
            if (taskItemId.HasValue)
            {
                sqlBuilder.AppendLine(@" AND detail.`LatestTaskItemId` = @TaskItemId");
            }

            // 添加排序和分页
            sqlBuilder.AppendLine(@" ORDER BY task.`Deadline` DESC");
            sqlBuilder.AppendLine(" LIMIT @Take OFFSET @Skip");

            countSqlBuilder.AppendLine(@"
                SELECT COUNT(*)
                FROM
                    `Ledger_LedgerDepartmentDataDetailStatistics` AS detail
                    LEFT JOIN `Platform_Departments` AS depart ON detail.`DepartmentId` = depart.`Id`
                    LEFT JOIN `Platform_Regions` AS region ON depart.`RegionId` = region.`Id`
                    LEFT JOIN `Platform_Regions` AS parent_region ON region.`ParentId` = parent_region.`Id`
                    INNER JOIN `Platform_WFTaskItems` AS task ON detail.`LatestTaskItemId` = task.`Id`
                    inner join `Ledger_LedgerDepartments` lld on (lld.`DepartmentId` = depart.`Id` and detail.`LedgerId`  = lld.`LedgerId`)
                WHERE
                    detail.`LedgerId` = @LedgerId
                    AND depart.`IsDeleted` = FALSE
                    AND region.`IsDeleted` = FALSE
                    AND detail.`LatestTaskItemId` is not null
                    AND task.`IsDeleted` = FALSE
                    ");

            // 为计数查询添加相同的条件
            if (grade.HasValue)
            {
                countSqlBuilder.AppendLine(@" AND region.`Grade` = @Grade");
            }

            if (!string.IsNullOrEmpty(filter))
            {
                countSqlBuilder.AppendLine(@" AND depart.`Name` LIKE LOWER(@Filter)");
            }

            if (fillProgressStatus != null)
            {
                countSqlBuilder.AppendLine(@" AND detail.`FillProgressStatus` = @FillProgressStatus");
            }

            if (!string.IsNullOrEmpty(districtId))
            {
                countSqlBuilder.AppendLine(@" AND region.`Id` IN @RegionIds");
            }

            if (type == LedgerDepartmentFillStatisticsType.Mine)
            {
                countSqlBuilder.AppendLine(
                    @" AND (detail.`DepartmentId` = @DepartmentId OR detail.`AuthDepartmentId` = @DepartmentId)");
            }
            else if (type == LedgerDepartmentFillStatisticsType.Counties)
            {
                countSqlBuilder.AppendLine(@" AND detail.`DepartmentId` IN @DepartmentIds");
            }

            // 如果taskItemId不为空
            if (taskItemId.HasValue)
            {
                countSqlBuilder.AppendLine(@" AND detail.`LatestTaskItemId` = @TaskItemId");
            }

            var parameters = new
            {
                LedgerId = ledgerId,
                Grade = grade,
                Filter = !string.IsNullOrEmpty(filter) ? $"%{filter}%" : null,
                FillProgressStatus = fillProgressStatus,
                RegionIds = districtId?.Split(',').Select(x => x.Trim()).ToArray(),
                DepartmentId = bigDepartmentId,
                DepartmentIds = departmentIds?.Select(s=>s.ToString())?.ToList(),
                TaskItemId = taskItemId,
                Skip = skip,
                Take = take
            };
            using var connection = await GetDbConnectionAsync();

            // 获取总条数
            var transaction = await GetDbTransactionAsync();
            var countCommand = new CommandDefinition(countSqlBuilder.ToString(), parameters, transaction: transaction);
            var totalCount = await connection.ExecuteScalarAsync<int>(countCommand);

            // 获取分页数据
            var queryCommand = new CommandDefinition(sqlBuilder.ToString(), parameters, transaction: transaction);
            var data = (await DbConnection
                    .QueryAsync<LedgerDepartmentDataDetailStatisticsView, Department, dynamic,
                        LedgerDepartmentDataDetailStatisticsView>( //
                        queryCommand,
                        (statistics, department, taskItem) => //
                        {
                            if (department != null)
                            {
                                statistics.Department = department;
                            }

                            if (taskItem != null)
                            {
                                statistics.TaskId = taskItem.Id == null ? null : (taskItem.Id is Guid ?  taskItem.Id : Guid.Parse(taskItem.Id)); // 因mysql存储的是文本，pgsql存储的是uuid，因此要适配两种情况
                                statistics.TaskName = taskItem.Name;
                                statistics.Deadline = taskItem.Deadline;
                            }

                            return statistics;
                        },
                        splitOn: "Id,Id")) // 在Department的Id字段和task的Id字段处分割 ,Id
                .ToList();

            return (data, totalCount);
        }
 
        public override async Task<(List<LedgerDepartmentFillView> Data, long TotalCount)>
            GetDataAndCountByFilterAsync(
                RegionGrade? grade,
                [CanBeNull] string filter,
                [CanBeNull] List<Guid> regionIds, [CanBeNull] List<Guid> departmentIds,
                Guid? bigDepartmentId,
                Guid? userId,
                int? reminderInterval,
                LedgerFillStatus? ledgerFillStatus,
                [CanBeNull] List<RegionGrade> departmentLevels, LedgerDepartmentFillStatisticsType type,
                [CanBeNull] List<Guid> regionsDepartmentIds,
                int skip,
                int take,
                MyLedgerDtoLitStatus? litStatus = null)
        {
            var sqlBuilder = new StringBuilder();
            sqlBuilder.AppendLine(@"
WITH filtered_data AS (
    SELECT 
        detail.`Id` as `DetailId`,
        detail.`FillProgressStatus`,
        detail.`StatisticsTime`,
        detail.`StatisticsUpdateTime`,
        detail.`TotalUpdateCount`,
        detail.`PeriodTotalUpdateCount`,
        l.`Id` as `LedgerId`,
        l.`Name` as `LedgerName`,
        l.`CreationTime`,
        l.`LastOnlineTime`,
        l.`Deadline`,
        l.`TaskInterval`,
        lt.`Id` as `LedgerTypeId`,
        lt.`Name` as `LedgerTypeName`
    FROM `Ledger_LedgerDepartmentDataDetailStatistics` detail
    INNER JOIN `Ledger_Ledgers` l ON detail.`LedgerId` = l.`Id` 
    INNER JOIN `Ledger_LedgerTypes` lt ON l.`LedgerTypeId` = lt.`Id` 
    INNER JOIN `Platform_Departments` dept ON lt.`DepartmentId` = dept.`Id` 
    INNER JOIN `Platform_Regions` region ON dept.`RegionId` = region.`Id` 
    INNER JOIN `Platform_WFPlanTasks` plt ON (detail.`LedgerId` = plt.`BindObjectId`
        AND detail.`LatestTaskItemId` = plt.`LatestTaskItemId`)
    INNER JOIN `Platform_Departments` pds ON detail.`DepartmentId` = pds.`Id`
    inner join `Ledger_LedgerDepartments` lld on (lld.`DepartmentId` = pds.`Id` and detail.`LedgerId`  = lld.`LedgerId`)
    WHERE l.`IsDeleted` IS FALSE
    AND lt.`IsDeleted` IS FALSE
    AND dept.`IsDeleted` IS FALSE
    AND region.`IsDeleted` IS FALSE
    AND l.`IsOnline` = true
    AND l.`ReminderInterval` > 0
    AND l.`Name` not LIKE '%【测试】%'
");

            if (reminderInterval.HasValue)
            {
                sqlBuilder.AppendLine(@"
    AND l.`ReminderInterval` = @ReminderInterval");
            }

            if (!string.IsNullOrEmpty(filter))
            {
                sqlBuilder.AppendLine(@"
    AND l.`Name` LIKE LOWER(@Filter)");
            }

            if (departmentIds != null && departmentIds!.Any())
            {
                sqlBuilder.AppendLine(@"
    AND lt.`Id` IN @DepartmentIds");
            }

            if (departmentLevels != null && departmentLevels!.Any())
            {
                sqlBuilder.AppendLine(@"
            AND EXISTS (
                SELECT 1 FROM `Platform_Regions` r 
                WHERE r.`Id` = pds.`RegionId` 
                AND r.`Grade` IN @DepartmentLevels
                AND r.`IsDeleted` IS FALSE
            )");
            }

            if (type == LedgerDepartmentFillStatisticsType.Mine)
            {
                if (userId.HasValue)
                {
                    sqlBuilder.AppendLine(@"
                    AND EXISTS (
                        SELECT 1 FROM `Ledger_LedgerUsers` lu 
                        WHERE lu.`LedgerId` = l.`Id` 
                        AND lu.`UserId` = @UserId 
                        AND lu.`DepartmentId` = @BigDepartmentId
                    )");
                }

                sqlBuilder.AppendLine(
                    @" AND (detail.`DepartmentId` = @BigDepartmentId OR detail.`AuthDepartmentId` = @BigDepartmentId)");
            }
            else if (type == LedgerDepartmentFillStatisticsType.Counties)
            {
                sqlBuilder.AppendLine(@" AND detail.`DepartmentId` IN @RegionsDepartmentIds");
                // sqlBuilder.AppendLine(@" AND detail.`DepartmentId` IN @RegionsDepartmentIds[])");
            }
            
            if (litStatus != null && litStatus.HasValue)
            {
                sqlBuilder.AppendLine(@" and lld.`LitStatus` = @LitStatus");
            }

            sqlBuilder.AppendLine(@"
),
grouped_data AS (
    SELECT 
        fd.`LedgerId`,
        MAX(fd.`LedgerName`) as `LedgerName`,
        MAX(fd.`CreationTime`) as `CreationTime`,
        CAST(COUNT(*) AS SIGNED) as `DepartmentCountByNeedUpdate`,
        CAST(COUNT(CASE WHEN fd.`FillProgressStatus` IN (1, 2) THEN 1 END) AS SIGNED) as `DepartmentCountByUpdated`,
        CAST(COUNT(CASE WHEN fd.`FillProgressStatus` NOT IN (1, 2) THEN 1 END) AS SIGNED) as `DepartmentCountByNotUpdated`,
        MAX(fd.`StatisticsTime`) as `StatisticsTime`,
        MAX(fd.`StatisticsUpdateTime`) as `StatisticsUpdateTime`,
        MAX(fd.`CreationTime`) as `sortField`
    FROM filtered_data fd
    GROUP BY fd.`LedgerId`
");
    
            if (ledgerFillStatus.HasValue)
            {
                if (ledgerFillStatus == LedgerFillStatus.NotFilled)
                {
                    sqlBuilder.AppendLine(@"
            HAVING CAST(COUNT(CASE WHEN fd.`FillProgressStatus` IN (1, 2) THEN 1 END) AS SIGNED) < CAST(COUNT(*) AS SIGNED)");
                }
                else if (ledgerFillStatus == LedgerFillStatus.Filled)
                {
                    sqlBuilder.AppendLine(@"
            HAVING CAST(COUNT(CASE WHEN fd.`FillProgressStatus` IN (1, 2) THEN 1 END) AS SIGNED) = CAST(COUNT(*) AS SIGNED)");
                }
            }
            
            sqlBuilder.AppendLine(@"
)
SELECT 
    gd.*,
    l.*,
    lt.`Id` as `LedgerTypeId`,
    lt.`Name` as `LedgerTypeName`,
    (SELECT COUNT(*) FROM grouped_data) as TotalCount
FROM grouped_data gd
INNER JOIN `Ledger_Ledgers` l ON gd.`LedgerId` = l.`Id` AND l.`IsDeleted` IS FALSE
INNER JOIN `Ledger_LedgerTypes` lt ON l.`LedgerTypeId` = lt.`Id` AND lt.`IsDeleted` IS FALSE
LIMIT @Take OFFSET @Skip");

            var parameters = new
            {
                RegionIds = regionIds?.Select(s=>s.ToString())?.ToList(),
                ReminderInterval = reminderInterval,
                Filter = !string.IsNullOrEmpty(filter) ? $"%{filter}%" : null,
                BigDepartmentId = bigDepartmentId,
                DepartmentIds = departmentIds?.Select(s=>s.ToString())?.ToList(),
                RegionsDepartmentIds = regionsDepartmentIds?.Select(s=>s.ToString())?.ToList(),
                Skip = skip,
                Take = take,
                UserId = userId,
                DepartmentLevels = departmentLevels?.Select(x => (int)x).ToArray(),
                LitStatus = litStatus
            };
            
            // // 打印完整SQL（包含参数值）
            // var completeSql = sqlBuilder.ToString();
            // foreach (var param in parameters.GetType().GetProperties())
            // {
            //     var paramName = param.Name;
            //     var paramValue = param.GetValue(parameters);
            //
            //     if (paramValue == DBNull.Value)
            //     {
            //         completeSql = completeSql.Replace($"@{paramName}", "NULL");
            //     }
            //     else if (paramValue is Array array)
            //     {
            //         var arrayValues = string.Join(", ", ((Array)paramValue).Cast<object>().Select(x => $"'{x}'"));
            //         completeSql = completeSql.Replace($"@{paramName}", $"ARRAY[{arrayValues}]");
            //     }
            //     else if (paramValue is string)
            //     {
            //         completeSql = completeSql.Replace($"@{paramName}", $"'{paramValue}'");
            //     }
            //     else
            //     {
            //         completeSql = completeSql.Replace($"@{paramName}", paramValue?.ToString() ?? "NULL");
            //     }
            // }
            // // 输出最终SQL
            // Console.WriteLine("\n============= 数据查询SQL =============\n");
            // Console.WriteLine(completeSql);
            // Console.WriteLine("\n============= 计数查询SQL =============\n");
            
            var connection = await GetDbConnectionAsync();
            var transaction = await GetDbTransactionAsync();
            var command = new CommandDefinition(sqlBuilder.ToString(), parameters, transaction: transaction);
            var result = await connection.QueryAsync<LedgerDepartmentFillView, dynamic, long,
                (LedgerDepartmentFillView View, long Total)>(
                command,
                (view, ledger, total) =>
                {
                    view.Ledger = new StatisticsLedgerView
                    {
                        Name = ledger.Name,
                        LastOnlineTime = ledger.LastOnlineTime,
                        LedgerType = ledger.LedgerTypeId != null
                            ? new StatisticsLedgerTypeView
                            {
                                Name = ledger.LedgerTypeName
                            }
                            : null,
                        Deadline = ledger.Deadline,
                        TaskInterval = ledger.TaskInterval
                    };
                    return (view, total);
                },
                splitOn: "Id,TotalCount");

            var items = result.Select(x => x.View).ToList();
            var totalCount = result.FirstOrDefault().Total;

            return (items, totalCount);
        }


        public override async Task<List<LedgerDepartmentDataDetailStatistics>>
            GetFillDataAsync(
                List<Guid> ledgerIds, LedgerDepartmentFillStatisticsType type,
                Guid bigDepartmentId, List<Guid> departmentIds, [CanBeNull] List<Guid> taskItemIds)
        {
            // 构建数据查询 SQL
            var sqlBuilder = new StringBuilder();


            sqlBuilder.AppendLine(@"
                SELECT
                    detail.*,
                    pds.`Id`,
                    pds.`Name`,
                    l.`Id` as `LedgerId`,
                    l.`Name` as `LedgerName`,
                    l.`CreationTime`,
                    l.`LastOnlineTime`,
                    l.`ReminderInterval`,
                    l.`ReminderDailyReminderTime`,
                    l.`ReminderDailyDeadlineTime`,
                    l.`ReminderWeeklyReminderDayOfWeek`,
                    l.`ReminderWeeklyStartDayOfWeek`,
                    l.`ReminderWeeklyDeadlineDayOfWeek`,
                    l.`ReminderUpHalfMonthStartTime`,
                    l.`ReminderDownHalfMonthStartTime`,
                    l.`ReminderMonthlyReminderDay`,
                    l.`ReminderMonthlyStartDay`,
                    l.`ReminderMonthlyDeadlineDay`,
                    l.`ReminderQuarterlyReminderDay1`,
                    l.`ReminderQuarterlyStartDay1`,
                    l.`ReminderQuarterlyDeadlineDay1`,
                    l.`ReminderQuarterlyReminderDay2`,
                    l.`ReminderQuarterlyStartDay2`,
                    l.`ReminderQuarterlyDeadlineDay2`,
                    l.`ReminderQuarterlyReminderDay3`,
                    l.`ReminderQuarterlyStartDay3`,
                    l.`ReminderQuarterlyDeadlineDay3`,
                    l.`ReminderQuarterlyReminderDay4`,
                    l.`ReminderQuarterlyStartDay4`,
                    l.`ReminderQuarterlyDeadlineDay4`,
                    l.`ReminderYearlyReminderDay`,
                    l.`ReminderYearlyStartDay`,
                    l.`ReminderYearlyDeadlineDay`,
                    l.`ReminderUpHalfYearStartTime`,
                    l.`ReminderDownHalfYearStartTime`,
                    l.`ReminderConfig_DownHalfYearDateOnlyTime`,
                    l.`ReminderConfig_DownHalfYearReminderTime`,
                    l.`ReminderConfig_UpHalfYearDateOnlyTime`,
                    l.`ReminderConfig_UpHalfYearReminderTime`,
                    l.`ReminderConfig_DownHalfMonthDateOnlyTime`,
                    l.`ReminderConfig_DownHalfMonthReminderTime`,
                    l.`ReminderConfig_UpHalfMonthDateOnlyTime`,
                    l.`ReminderConfig_UpHalfMonthReminderTime`
                FROM
                    `Ledger_LedgerDepartmentDataDetailStatistics` AS detail
                    INNER JOIN `Ledger_Ledgers` l ON detail.`LedgerId` = l.`Id` 
                    INNER JOIN `Ledger_LedgerTypes` lt ON l.`LedgerTypeId` = lt.`Id` 
                    INNER JOIN `Platform_Departments` dept ON lt.`DepartmentId` = dept.`Id` 
                    INNER JOIN `Platform_Regions` region ON dept.`RegionId` = region.`Id` 
                    INNER JOIN `Platform_WFPlanTasks` plt ON (detail.`LedgerId` = plt.`BindObjectId`
                        AND detail.`LatestTaskItemId` = plt.`LatestTaskItemId`)
                    INNER JOIN `Platform_Departments` pds ON detail.`DepartmentId` = pds.`Id`
                    inner join `Ledger_LedgerDepartments` lld on (lld.`DepartmentId` = pds.`Id` and detail.`LedgerId`  = lld.`LedgerId`)
                    WHERE l.`IsDeleted` IS FALSE
                    AND lt.`IsDeleted` IS FALSE
                    AND dept.`IsDeleted` IS FALSE
                    AND region.`IsDeleted` IS FALSE
                    AND l.`IsOnline` = true
                    AND l.`ReminderInterval` > 0
                    AND l.`Name` not LIKE '%【测试】%'
               
                    ");

            if (ledgerIds.Count > 0)
            {
                var ledgerIdsArray = string.Join(",", ledgerIds.Select(id => $"'{id}'"));
                sqlBuilder.AppendLine($" AND detail.`LedgerId` IN ({ledgerIdsArray})");
            }

            if (type == LedgerDepartmentFillStatisticsType.Mine)
            {
                sqlBuilder.AppendLine(
                    @" AND (detail.`DepartmentId` = @DepartmentId OR detail.`AuthDepartmentId` = @DepartmentId)");
            }
            else if (type == LedgerDepartmentFillStatisticsType.Counties && departmentIds != null)
            {
                // sqlBuilder.AppendLine(@" AND detail.`DepartmentId` IN @DepartmentIds");
                var departmentIdsArray = string.Join(",", departmentIds.Select(d => $"'{d}'"));
                sqlBuilder.AppendLine($" AND detail.`DepartmentId` IN ({departmentIdsArray})");
            }

            // 如果taskItemId不为空
            if (taskItemIds != null && taskItemIds.Count > 0)
            {
                // sqlBuilder.AppendLine(@" AND detail.`LatestTaskItemId` IN @TaskItemIds[])");
                var taskItemIdsArray = string.Join(",", taskItemIds.Select(id => $"'{id}'"));
                sqlBuilder.AppendLine($" AND detail.`LatestTaskItemId` IN ({taskItemIdsArray})");
            }

            // 添加排序和分页
            sqlBuilder.AppendLine(@" ORDER BY detail.`StatisticsTime` DESC");

            var parameters = new
            {
                LedgerIds = ledgerIds,
                DepartmentId = bigDepartmentId,
                DepartmentIds = departmentIds?.Select(s=>s.ToString())?.ToList(),
                TaskItemIds = taskItemIds?.Select(s=>s.ToString())?.ToList(),
            };

            try
            {
                // 创建一个用于打印最终SQL的变量
                var finalSql = new StringBuilder(sqlBuilder.ToString());
                
                // 创建参数字典用于替换
                var paramDict = new Dictionary<string, string>();
                
                // 添加参数值到字典中
                if (ledgerIds.Count > 0)
                {
                    var uuidArray = string.Join(",", ledgerIds.Select(id => $"'{id}'"));
                    paramDict.Add("@LedgerIds[]", $"ARRAY[{uuidArray}][]");
                }
                
                if (type == LedgerDepartmentFillStatisticsType.Mine)
                {
                    paramDict.Add("@DepartmentId", $"'{bigDepartmentId}'");
                }
                else if (type == LedgerDepartmentFillStatisticsType.Counties && departmentIds?.Any() == true)
                {
                    var uuidArray = string.Join(",", departmentIds.Select(d => $"'{d}'"));
                    paramDict.Add("@DepartmentIds[]", $"ARRAY[{uuidArray}][]");
                }
                // 替换参数
                string printableSql = finalSql.ToString();
                foreach (var param in paramDict)
                {
                    printableSql = printableSql.Replace(param.Key, param.Value);
                }
                
                // 打印最终可执行的SQL
                Console.WriteLine("\n============= GetFillDataAsync 最终SQL =============\n");
                Console.WriteLine(printableSql);
                Console.WriteLine("\n==============================================\n");
                
                //修改原始SQL，避免类型转换错误
                string modifiedSql = sqlBuilder.ToString();
                
                // 只替换与uuid[]相关的参数
                if (ledgerIds.Count > 0)
                {
                    var ledgerIdsArray = string.Join(",", ledgerIds.Select(id => $"'{id}'"));
                    modifiedSql = modifiedSql.Replace(" AND detail.`LedgerId` IN @LedgerIds[])", 
                        $" AND detail.`LedgerId` IN ({ledgerIdsArray})");
                }
                
                if (type == LedgerDepartmentFillStatisticsType.Counties && departmentIds?.Any() == true)
                {
                    var departmentIdsArray = string.Join(",", departmentIds.Select(d => $"'{d}'"));
                    modifiedSql = modifiedSql.Replace(" AND detail.`DepartmentId` IN @DepartmentIds[])", 
                        $" AND detail.`DepartmentId` IN ({departmentIdsArray})");
                }
                
                // 如果taskItemIds不为空，也替换它
                if (taskItemIds != null && taskItemIds.Count > 0)
                {
                    var taskItemIdsArray = string.Join(",", taskItemIds.Select(id => $"'{id}'"));
                    modifiedSql = modifiedSql.Replace(" AND detail.`LatestTaskItemId` IN @TaskItemIds[])", 
                        $" AND detail.`LatestTaskItemId` IN ({taskItemIdsArray})");
                }
                
                sqlBuilder = new StringBuilder(modifiedSql);
                
                var connection = await GetDbConnectionAsync();
                var transaction = await GetDbTransactionAsync();

                // 获取数据
                var data = (await connection
                    .QueryAsync<LedgerDepartmentDataDetailStatistics, Department, dynamic,
                        LedgerDepartmentDataDetailStatistics>(
                        sqlBuilder.ToString(),
                        (statistics, department, ledger) =>
                        {
                            if (department != null)
                            {
                                statistics.Department = department;
                            }

                            // 映射Ledger对象
                            statistics.Ledger = new Ledger.Core.Ledger(Guid.Parse(ledger.LedgerId.ToString()))
                            {
                                Name = ledger.LedgerName,
                                LastOnlineTime = ledger.LastOnlineTime,
                                ReminderConfig = new ReminderConfig
                                {
                                    Interval = (ReminderInterval)ledger.ReminderInterval,
                                    DailyReminderTime = ledger.ReminderDailyReminderTime,
                                    DailyDeadlineTime = ledger.ReminderDailyDeadlineTime,
                                    WeeklyReminderDayOfWeek = ledger.ReminderWeeklyReminderDayOfWeek,
                                    WeeklyStartDayOfWeek = ledger.ReminderWeeklyStartDayOfWeek,
                                    WeeklyDeadlineDayOfWeek = ledger.ReminderWeeklyDeadlineDayOfWeek,
                                    UpHalfMonthStartTime = ledger.ReminderUpHalfMonthStartTime,
                                    DownHalfMonthStartTime = ledger.ReminderDownHalfMonthStartTime,
                                    MonthlyReminderDay = ledger.ReminderMonthlyReminderDay,
                                    MonthlyStartDay = ledger.ReminderMonthlyStartDay,
                                    MonthlyDeadlineDay = ledger.ReminderMonthlyDeadlineDay,
                                    QuarterlyReminderDay1 = ledger.ReminderQuarterlyReminderDay1 == null
                                        ? null
                                        : DateOnly.FromDateTime(
                                            Convert.ToDateTime(ledger.ReminderQuarterlyReminderDay1)),
                                    QuarterlyStartDay1 = ledger.ReminderQuarterlyStartDay1 == null
                                        ? null
                                        : DateOnly.FromDateTime(Convert.ToDateTime(ledger.ReminderQuarterlyStartDay1)),
                                    QuarterlyDeadlineDay1 = ledger.ReminderQuarterlyDeadlineDay1 == null
                                        ? null
                                        : DateOnly.FromDateTime(
                                            Convert.ToDateTime(ledger.ReminderQuarterlyDeadlineDay1)),
                                    QuarterlyReminderDay2 = ledger.ReminderQuarterlyReminderDay2 == null
                                        ? null
                                        : DateOnly.FromDateTime(
                                            Convert.ToDateTime(ledger.ReminderQuarterlyReminderDay2)),
                                    QuarterlyStartDay2 = ledger.ReminderQuarterlyStartDay2 == null
                                        ? null
                                        : DateOnly.FromDateTime(Convert.ToDateTime(ledger.ReminderQuarterlyStartDay2)),
                                    QuarterlyDeadlineDay2 = ledger.ReminderQuarterlyDeadlineDay2 == null
                                        ? null
                                        : DateOnly.FromDateTime(
                                            Convert.ToDateTime(ledger.ReminderQuarterlyDeadlineDay2)),
                                    QuarterlyReminderDay3 = ledger.ReminderQuarterlyReminderDay3 == null
                                        ? null
                                        : DateOnly.FromDateTime(
                                            Convert.ToDateTime(ledger.ReminderQuarterlyReminderDay3)),
                                    QuarterlyStartDay3 = ledger.ReminderQuarterlyStartDay3 == null
                                        ? null
                                        : DateOnly.FromDateTime(Convert.ToDateTime(ledger.ReminderQuarterlyStartDay3)),
                                    QuarterlyDeadlineDay3 = ledger.ReminderQuarterlyDeadlineDay3 == null
                                        ? null
                                        : DateOnly.FromDateTime(
                                            Convert.ToDateTime(ledger.ReminderQuarterlyDeadlineDay3)),
                                    QuarterlyReminderDay4 = ledger.ReminderQuarterlyReminderDay4 == null
                                        ? null
                                        : DateOnly.FromDateTime(
                                            Convert.ToDateTime(ledger.ReminderQuarterlyReminderDay4)),
                                    QuarterlyStartDay4 = ledger.ReminderQuarterlyStartDay4 == null
                                        ? null
                                        : DateOnly.FromDateTime(Convert.ToDateTime(ledger.ReminderQuarterlyStartDay4)),
                                    QuarterlyDeadlineDay4 = ledger.ReminderQuarterlyDeadlineDay4 == null
                                        ? null
                                        : DateOnly.FromDateTime(
                                            Convert.ToDateTime(ledger.ReminderQuarterlyDeadlineDay4)),
                                    YearlyReminderDay = ledger.ReminderYearlyReminderDay == null
                                        ? null
                                        : DateOnly.FromDateTime(Convert.ToDateTime(ledger.ReminderYearlyReminderDay)),
                                    YearlyStartDay = ledger.ReminderYearlyStartDay == null
                                        ? null
                                        : DateOnly.FromDateTime(Convert.ToDateTime(ledger.ReminderYearlyStartDay)),
                                    YearlyDeadlineDay = ledger.ReminderYearlyDeadlineDay == null
                                        ? null
                                        : DateOnly.FromDateTime(Convert.ToDateTime(ledger.ReminderYearlyDeadlineDay)),
                                    UpHalfYearStartTime = ledger.ReminderUpHalfYearStartTime == null
                                        ? null
                                        : DateOnly.FromDateTime(Convert.ToDateTime(ledger.ReminderUpHalfYearStartTime)),
                                    DownHalfYearStartTime = ledger.ReminderDownHalfYearStartTime == null
                                        ? null
                                        : DateOnly.FromDateTime(
                                            Convert.ToDateTime(ledger.ReminderDownHalfYearStartTime)),
                                    DownHalfYearDateOnlyTime = ledger.ReminderConfig_DownHalfYearDateOnlyTime == null
                                        ? null
                                        : DateOnly.FromDateTime(
                                            Convert.ToDateTime(ledger.ReminderConfig_DownHalfYearDateOnlyTime)),
                                    DownHalfYearReminderTime = ledger.ReminderConfig_DownHalfYearReminderTime == null
                                        ? null
                                        : DateOnly.FromDateTime(
                                            Convert.ToDateTime(ledger.ReminderConfig_DownHalfYearReminderTime)),
                                    UpHalfYearDateOnlyTime = ledger.ReminderConfig_UpHalfYearDateOnlyTime == null
                                        ? null
                                        : DateOnly.FromDateTime(
                                            Convert.ToDateTime(ledger.ReminderConfig_UpHalfYearDateOnlyTime)),
                                    UpHalfYearReminderTime = ledger.ReminderConfig_UpHalfYearReminderTime == null
                                        ? null
                                        : DateOnly.FromDateTime(
                                            Convert.ToDateTime(ledger.ReminderConfig_UpHalfYearReminderTime)),
                                    DownHalfMonthDateOnlyTime = ledger.ReminderConfig_DownHalfMonthDateOnlyTime ?? 0,
                                    DownHalfMonthReminderTime = ledger.ReminderConfig_DownHalfMonthReminderTime ?? 0,
                                    UpHalfMonthDateOnlyTime = ledger.ReminderConfig_UpHalfMonthDateOnlyTime ?? 0,
                                    UpHalfMonthReminderTime = ledger.ReminderConfig_UpHalfMonthReminderTime ?? 0
                                }
                            };

                            return statistics;
                        },
                        parameters,
                        transaction: transaction,
                        splitOn: "Id,LedgerId")).ToList();

                return data;
            }
            catch (Exception ex)
            {
                throw;
            }
        }
    }
}