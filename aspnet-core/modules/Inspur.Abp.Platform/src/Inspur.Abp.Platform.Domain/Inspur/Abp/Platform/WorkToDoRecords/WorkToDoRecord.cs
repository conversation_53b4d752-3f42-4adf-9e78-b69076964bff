using System;
using System.Text.Json;
using Volo.Abp.Domain.Entities.Auditing;

namespace Inspur.Abp.Platform.WorkToDoRecords
{
    /// <summary>
    /// 通用待办记录表
    /// </summary>
    public class WorkToDoRecord : FullAuditedEntity<Guid>
    {
        /// <summary>
        /// 记录名称
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// 创建人名称
        /// </summary>
        public string CreaterName { get; set; }

        /// <summary>
        /// 创建部门名称
        /// </summary>
        public string CreaterDepartmentName { get; set; }

        /// <summary>
        /// 创建大部们名称
        /// </summary>
        public string CreaterLargeDepartmentName { get; set; }

        /// <summary>
        /// 处理人Id
        /// </summary>
        public Guid OperatorId { get; set; }

        /// <summary>
        /// 处理人部门所在Id
        /// </summary>
        public Guid? OperatorDepartmentId { get; set; }

        /// <summary>
        /// 处理人所在大部门Id
        /// </summary>
        public Guid OperatorLargeDepartmentId { get; set; }

        /// <summary>
        /// 业务Id
        /// </summary>
        public Guid BusinessId { get; set; }
        
        /// <summary>
        /// 业务类型
        /// </summary>
        public string BusinessType { get; set; }

        /// <summary>
        /// 报表任务Id
        /// </summary>
        public Guid? ReportTaskId { get; set; }

        /// <summary>
        /// 区域组织单位Id
        /// </summary>
        public Guid? AreaOrganizationUnitId { get; set; }

        /// <summary>
        /// 报表任务区域组织单位Id
        /// </summary>
        public Guid? ReportTaskAreaOrganizationUnitId { get; set; }

        /// <summary>
        /// 业务拓展Json
        /// </summary>
        public JsonDocument BusinessExtend { get; set; }

        /// <summary>
        /// 待办状态
        /// </summary>
        public WorkToDoRecordStatus Status { get; set; } = WorkToDoRecordStatus.PendingProcessing;

        /// <summary>
        /// 类别：报表1 台账2
        /// </summary>
        public WorkToDoRecordCategory Category { get; set; }

        /// <summary>
        /// 结束时间
        /// </summary>
        public DateTime? EndTime { get; set; }
        
        /// <summary>
        /// 完成时间
        /// </summary>
        public DateTime? CompletedTime { get; set; }

        protected WorkToDoRecord()
        {

        }

        public WorkToDoRecord
            (Guid id,
            string name,
            string createrName,
            string createrDepartmentName,
            string createrLargeDepartmentName,
            Guid operatorId,
            Guid? operatorDepartmentId,
            Guid operatorLargeDepartmentId,
            Guid businessId,
            string businessType,
            WorkToDoRecordCategory category
            ) : base(id)
        {
            Name = name;
            CreaterName = createrName;
            CreaterDepartmentName = createrDepartmentName;
            CreaterLargeDepartmentName = createrLargeDepartmentName;
            OperatorId = operatorId;
            OperatorDepartmentId = operatorDepartmentId;
            OperatorLargeDepartmentId = operatorLargeDepartmentId;
            BusinessId = businessId;
            BusinessType = businessType;
            Category = category;
        }

        public void SetStatus(WorkToDoRecordStatus status)
        {
            Status = status;
        }

        public void ConfirmComplete(DateTime completedTime)
        {
            this.CompletedTime = completedTime;
            this.Status = WorkToDoRecordStatus.Completed;
        }
    }
}
