﻿// <auto-generated />
using System;
using Inspur.Abp.Initializer.Data;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Volo.Abp.EntityFrameworkCore;

#nullable disable

namespace Inspur.Abp.Initializer.Migrations.InitializerPlatform
{
    [DbContext(typeof(InitializerPlatformDbContext))]
    partial class InitializerPlatformDbContextModelSnapshot : ModelSnapshot
    {
        protected override void BuildModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("_Abp_DatabaseProvider", EfCoreDatabaseProvider.MySql)
                .HasAnnotation("ProductVersion", "6.0.22")
                .HasAnnotation("Relational:MaxIdentifierLength", 64);

            modelBuilder.Entity("Inspur.Abp.Platform.Charts.Chart", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("char(36)");

                    b.Property<Guid>("ChartGroupId")
                        .HasColumnType("char(36)")
                        .HasComment("所属图表组Id");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("CreationTime");

                    b.Property<Guid?>("CreatorId")
                        .HasColumnType("char(36)")
                        .HasColumnName("CreatorId");

                    b.Property<Guid?>("DeleterId")
                        .HasColumnType("char(36)")
                        .HasColumnName("DeleterId");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("DeletionTime");

                    b.Property<string>("IdCode")
                        .HasMaxLength(256)
                        .HasColumnType("varchar(256)");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("tinyint(1)")
                        .HasDefaultValue(false)
                        .HasColumnName("IsDeleted");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("LastModificationTime");

                    b.Property<Guid?>("LastModifierId")
                        .HasColumnType("char(36)")
                        .HasColumnName("LastModifierId");

                    b.Property<string>("Name")
                        .HasMaxLength(256)
                        .HasColumnType("varchar(256)")
                        .HasComment("图表名称");

                    b.Property<string>("Scope")
                        .HasMaxLength(256)
                        .HasColumnType("varchar(256)")
                        .HasComment("范围");

                    b.Property<int>("Type")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("ChartGroupId");

                    b.ToTable("Platform_Charts", (string)null);
                });

            modelBuilder.Entity("Inspur.Abp.Platform.Charts.ChartDicData", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("char(36)");

                    b.Property<Guid>("ChartId")
                        .HasColumnType("char(36)")
                        .HasComment("所属图表Id");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("CreationTime");

                    b.Property<Guid?>("CreatorId")
                        .HasColumnType("char(36)")
                        .HasColumnName("CreatorId");

                    b.Property<string>("DataType")
                        .HasMaxLength(64)
                        .HasColumnType("varchar(64)")
                        .HasComment("数据类型,如：int,string,bool等");

                    b.Property<string>("IdCode")
                        .HasMaxLength(256)
                        .HasColumnType("varchar(256)");

                    b.Property<string>("Key")
                        .IsRequired()
                        .HasMaxLength(512)
                        .HasColumnType("varchar(512)")
                        .HasComment("图表数据项Key");

                    b.Property<string>("Value")
                        .HasColumnType("longtext")
                        .HasComment("图表数据项Value");

                    b.HasKey("Id");

                    b.HasIndex("ChartId");

                    b.ToTable("Platform_ChartDicDatas", (string)null);
                });

            modelBuilder.Entity("Inspur.Abp.Platform.Charts.ChartGroup", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("char(36)");

                    b.Property<int>("Code")
                        .HasColumnType("int")
                        .HasComment("图表组代码");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("CreationTime");

                    b.Property<Guid?>("CreatorId")
                        .HasColumnType("char(36)")
                        .HasColumnName("CreatorId");

                    b.Property<string>("IdCode")
                        .HasMaxLength(256)
                        .HasColumnType("varchar(256)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("varchar(256)")
                        .HasComment("图表组名称");

                    b.Property<int>("Sort")
                        .HasColumnType("int")
                        .HasComment("排序，从小到大");

                    b.HasKey("Id");

                    b.ToTable("Platform_ChartGroups", (string)null);
                });

            modelBuilder.Entity("Inspur.Abp.Platform.Charts.ChartListData", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("char(36)");

                    b.Property<Guid>("ChartId")
                        .HasColumnType("char(36)")
                        .HasComment("所属图表Id");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("CreationTime");

                    b.Property<Guid?>("CreatorId")
                        .HasColumnType("char(36)")
                        .HasColumnName("CreatorId");

                    b.Property<string>("ExtendData")
                        .HasColumnType("json");

                    b.Property<string>("Field1")
                        .HasMaxLength(1024)
                        .HasColumnType("varchar(1024)")
                        .HasComment("列表字段1");

                    b.Property<string>("Field2")
                        .HasMaxLength(1024)
                        .HasColumnType("varchar(1024)")
                        .HasComment("列表字段2");

                    b.Property<string>("Field3")
                        .HasMaxLength(1024)
                        .HasColumnType("varchar(1024)")
                        .HasComment("列表字段3");

                    b.Property<string>("Field4")
                        .HasMaxLength(1024)
                        .HasColumnType("varchar(1024)")
                        .HasComment("列表字段4");

                    b.Property<string>("Field5")
                        .HasMaxLength(1024)
                        .HasColumnType("varchar(1024)")
                        .HasComment("列表字段5");

                    b.Property<string>("IdCode")
                        .HasMaxLength(256)
                        .HasColumnType("varchar(256)");

                    b.Property<int>("Sort")
                        .HasColumnType("int")
                        .HasComment("列表排序，从小到大");

                    b.HasKey("Id");

                    b.HasIndex("ChartId");

                    b.ToTable("Platform_ChartListDatas", (string)null);
                });

            modelBuilder.Entity("Inspur.Abp.Platform.Charts.ChartTimeFlowData", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("char(36)");

                    b.Property<DateTime>("AggregateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<Guid>("ChartId")
                        .HasColumnType("char(36)")
                        .HasComment("所属图表Id");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("CreationTime");

                    b.Property<Guid?>("CreatorId")
                        .HasColumnType("char(36)")
                        .HasColumnName("CreatorId");

                    b.Property<int>("Day")
                        .HasColumnType("int")
                        .HasComment("日期");

                    b.Property<int>("Hour")
                        .HasColumnType("int")
                        .HasComment("小时");

                    b.Property<string>("IdCode")
                        .HasMaxLength(256)
                        .HasColumnType("varchar(256)");

                    b.Property<int>("Minute")
                        .HasColumnType("int")
                        .HasComment("分钟");

                    b.Property<int>("Month")
                        .HasColumnType("int")
                        .HasComment("月份");

                    b.Property<string>("Name")
                        .HasMaxLength(256)
                        .HasColumnType("varchar(256)")
                        .HasComment("名称");

                    b.Property<int>("Second")
                        .HasColumnType("int")
                        .HasComment("秒");

                    b.Property<int>("TimePeriodType")
                        .HasColumnType("int");

                    b.Property<string>("Unit")
                        .HasMaxLength(64)
                        .HasColumnType("varchar(64)")
                        .HasComment("单位");

                    b.Property<decimal>("Value")
                        .HasColumnType("decimal(65,30)")
                        .HasComment("数据值");

                    b.Property<int>("Week")
                        .HasColumnType("int")
                        .HasComment("周");

                    b.Property<int>("Year")
                        .HasColumnType("int")
                        .HasComment("年度");

                    b.HasKey("Id");

                    b.HasIndex("ChartId");

                    b.ToTable("Platform_ChartTimeFlowDatas", (string)null);
                });

            modelBuilder.Entity("Inspur.Abp.Platform.Organizational.Department", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("char(36)");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .HasMaxLength(40)
                        .HasColumnType("varchar(40)")
                        .HasColumnName("ConcurrencyStamp");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("CreationTime");

                    b.Property<Guid?>("CreatorId")
                        .HasColumnType("char(36)")
                        .HasColumnName("CreatorId");

                    b.Property<Guid?>("DeleterId")
                        .HasColumnType("char(36)")
                        .HasColumnName("DeleterId");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("DeletionTime");

                    b.Property<Guid?>("DepartmentExtendId")
                        .HasColumnType("char(36)")
                        .HasComment("内设(机构)部门Id");

                    b.Property<string>("ExtraProperties")
                        .HasColumnType("longtext")
                        .HasColumnName("ExtraProperties");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("tinyint(1)")
                        .HasDefaultValue(false)
                        .HasColumnName("IsDeleted");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("LastModificationTime");

                    b.Property<Guid?>("LastModifierId")
                        .HasColumnType("char(36)")
                        .HasColumnName("LastModifierId");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("varchar(256)")
                        .HasComment("部门名称");

                    b.Property<Guid?>("ParentId")
                        .HasColumnType("char(36)");

                    b.Property<Guid?>("RegionId")
                        .HasColumnType("char(36)");

                    b.Property<string>("TreeCode")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)")
                        .HasComment("部门TreeCode用于排序、和区分部门层级如：\"00001.00001\"");

                    b.Property<long?>("YkzOrgUnitId")
                        .HasColumnType("bigint");

                    b.Property<long?>("YkzOrganizationId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("DepartmentExtendId");

                    b.HasIndex("ParentId");

                    b.HasIndex("RegionId");

                    b.HasIndex("TreeCode");

                    b.HasIndex("YkzOrgUnitId")
                        .IsUnique();

                    b.HasIndex("YkzOrganizationId")
                        .IsUnique();

                    b.ToTable("Platform_Departments", (string)null);
                });

            modelBuilder.Entity("Inspur.Abp.Platform.Organizational.DepartmentFavorite", b =>
                {
                    b.Property<Guid>("DepartmentFavoriteGroupId")
                        .HasColumnType("char(36)");

                    b.Property<Guid>("DepartmentId")
                        .HasColumnType("char(36)");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("CreationTime");

                    b.Property<Guid?>("CreatorId")
                        .HasColumnType("char(36)")
                        .HasColumnName("CreatorId");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("longtext")
                        .HasComment("部门名称");

                    b.Property<int>("Sort")
                        .HasColumnType("int")
                        .HasComment("排序从小到大");

                    b.HasKey("DepartmentFavoriteGroupId", "DepartmentId");

                    b.ToTable("Platform_DepartmentFavorites", (string)null);
                });

            modelBuilder.Entity("Inspur.Abp.Platform.Organizational.DepartmentFavoriteGroup", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("char(36)");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("CreationTime");

                    b.Property<Guid?>("CreatorId")
                        .HasColumnType("char(36)")
                        .HasColumnName("CreatorId");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)")
                        .HasComment("分组名称");

                    b.Property<int>("Sort")
                        .HasColumnType("int")
                        .HasComment("排序从小到大");

                    b.Property<Guid>("UserId")
                        .HasColumnType("char(36)");

                    b.HasKey("Id");

                    b.HasIndex("UserId");

                    b.ToTable("Platform_DepartmentFavoriteGroups", (string)null);
                });

            modelBuilder.Entity("Inspur.Abp.Platform.Organizational.DepartmentGroup", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("char(36)");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("CreationTime");

                    b.Property<Guid?>("CreatorId")
                        .HasColumnType("char(36)")
                        .HasColumnName("CreatorId");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)")
                        .HasComment("部门组名称（条线名称）");

                    b.Property<Guid?>("StartDepartmentId")
                        .HasColumnType("char(36)");

                    b.HasKey("Id");

                    b.HasIndex("StartDepartmentId");

                    b.ToTable("Platform_DepartmentGroups", (string)null);
                });

            modelBuilder.Entity("Inspur.Abp.Platform.Organizational.DepartmentGroupDepartment", b =>
                {
                    b.Property<Guid>("DepartmentGroupId")
                        .HasColumnType("char(36)");

                    b.Property<Guid>("DepartmentId")
                        .HasColumnType("char(36)");

                    b.Property<Guid?>("ParentId")
                        .HasColumnType("char(36)");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("CreationTime");

                    b.Property<Guid?>("CreatorId")
                        .HasColumnType("char(36)")
                        .HasColumnName("CreatorId");

                    b.HasKey("DepartmentGroupId", "DepartmentId", "ParentId");

                    b.HasIndex("DepartmentId");

                    b.ToTable("Platform_DepartmentGroupDepartments", (string)null);
                });

            modelBuilder.Entity("Inspur.Abp.Platform.Organizational.Region", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("char(36)");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("varchar(256)")
                        .HasComment("区域代码");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .HasMaxLength(40)
                        .HasColumnType("varchar(40)")
                        .HasColumnName("ConcurrencyStamp");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("CreationTime");

                    b.Property<Guid?>("CreatorId")
                        .HasColumnType("char(36)")
                        .HasColumnName("CreatorId");

                    b.Property<Guid?>("DeleterId")
                        .HasColumnType("char(36)")
                        .HasColumnName("DeleterId");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("DeletionTime");

                    b.Property<string>("ExtraProperties")
                        .HasColumnType("longtext")
                        .HasColumnName("ExtraProperties");

                    b.Property<int>("Grade")
                        .HasColumnType("int")
                        .HasComment("区域级别,从1开始");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("tinyint(1)")
                        .HasDefaultValue(false)
                        .HasColumnName("IsDeleted");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("LastModificationTime");

                    b.Property<Guid?>("LastModifierId")
                        .HasColumnType("char(36)")
                        .HasColumnName("LastModifierId");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("varchar(256)")
                        .HasComment("区域名称");

                    b.Property<string>("ParentCode")
                        .HasMaxLength(256)
                        .HasColumnType("varchar(256)")
                        .HasComment("父级区域代码");

                    b.Property<Guid?>("ParentId")
                        .HasColumnType("char(36)");

                    b.Property<string>("ParentTelecomCode")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)")
                        .HasComment("父级电信区域Code");

                    b.Property<int>("RegionType")
                        .HasColumnType("int");

                    b.Property<string>("Remark")
                        .HasMaxLength(1024)
                        .HasColumnType("varchar(1024)")
                        .HasComment("说明");

                    b.Property<string>("ShortCode")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("varchar(256)")
                        .HasComment("区域短代码");

                    b.Property<string>("TelecomCode")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)")
                        .HasComment("电信区域Code");

                    b.Property<string>("TreeCode")
                        .IsRequired()
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)")
                        .HasComment("部门TreeCode用于排序、和区分部门层级如：\"00001.00001\"");

                    b.HasKey("Id");

                    b.HasIndex("Code");

                    b.HasIndex("ParentCode");

                    b.HasIndex("ParentId");

                    b.HasIndex("ParentTelecomCode");

                    b.HasIndex("TelecomCode");

                    b.HasIndex("TreeCode");

                    b.ToTable("Platform_Regions", (string)null);
                });

            modelBuilder.Entity("Inspur.Abp.Platform.Organizational.RegionFavorite", b =>
                {
                    b.Property<Guid>("RegionFavoriteGroupId")
                        .HasColumnType("char(36)");

                    b.Property<Guid>("RegiontId")
                        .HasColumnType("char(36)");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("CreationTime");

                    b.Property<Guid?>("CreatorId")
                        .HasColumnType("char(36)")
                        .HasColumnName("CreatorId");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)")
                        .HasComment("区域名称");

                    b.Property<int>("Sort")
                        .HasColumnType("int")
                        .HasComment("排序从小到大");

                    b.HasKey("RegionFavoriteGroupId", "RegiontId");

                    b.ToTable("Platform_RegionFavorites", (string)null);
                });

            modelBuilder.Entity("Inspur.Abp.Platform.Organizational.RegionFavoriteGroup", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("char(36)");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("CreationTime");

                    b.Property<Guid?>("CreatorId")
                        .HasColumnType("char(36)")
                        .HasColumnName("CreatorId");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)")
                        .HasComment("分组名称");

                    b.Property<int>("Sort")
                        .HasColumnType("int")
                        .HasComment("排序从小到大");

                    b.Property<Guid>("UserId")
                        .HasColumnType("char(36)");

                    b.HasKey("Id");

                    b.HasIndex("UserId");

                    b.ToTable("Platform_RegionFavoriteGroups", (string)null);
                });

            modelBuilder.Entity("Inspur.Abp.Platform.Organizational.UserDepartment", b =>
                {
                    b.Property<Guid>("UserId")
                        .HasColumnType("char(36)")
                        .HasComment("用户Id");

                    b.Property<Guid>("DepartmentId")
                        .HasColumnType("char(36)")
                        .HasComment("部门Id");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .HasMaxLength(40)
                        .HasColumnType("varchar(40)")
                        .HasColumnName("ConcurrencyStamp");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("CreationTime");

                    b.Property<Guid?>("CreatorId")
                        .HasColumnType("char(36)")
                        .HasColumnName("CreatorId");

                    b.Property<string>("ExtraProperties")
                        .HasColumnType("longtext")
                        .HasColumnName("ExtraProperties");

                    b.HasKey("UserId", "DepartmentId");

                    b.HasIndex("DepartmentId");

                    b.HasIndex("UserId");

                    b.ToTable("Platform_UserDepartments", (string)null);
                });

            modelBuilder.Entity("Inspur.Abp.Platform.Organizational.UserDepartmentFavoriteGroup", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("char(36)");

                    b.Property<Guid>("BigDepartmentId")
                        .HasColumnType("char(36)")
                        .HasComment("大部门Id");

                    b.Property<Guid>("DepartmentId")
                        .HasColumnType("char(36)")
                        .HasComment("部门ID");

                    b.Property<Guid>("FavoriteId")
                        .HasColumnType("char(36)");

                    b.Property<Guid>("FavoriteUserId")
                        .HasColumnType("char(36)")
                        .HasComment("收藏人id");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)")
                        .HasComment("收藏组名称");

                    b.Property<Guid>("UserId")
                        .HasColumnType("char(36)")
                        .HasComment("用户id");

                    b.HasKey("Id");

                    b.HasIndex("FavoriteId");

                    b.ToTable("Platform_UserDepartmentFavoriteGroups", (string)null);
                });

            modelBuilder.Entity("Inspur.Abp.Platform.Organizational.UserDepartmentRole", b =>
                {
                    b.Property<Guid>("UserId")
                        .HasColumnType("char(36)")
                        .HasComment("用户Id");

                    b.Property<Guid>("DepartmentId")
                        .HasColumnType("char(36)")
                        .HasComment("部门Id");

                    b.Property<Guid>("RoleId")
                        .HasColumnType("char(36)")
                        .HasComment("角色Id");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("CreationTime");

                    b.Property<Guid?>("CreatorId")
                        .HasColumnType("char(36)")
                        .HasColumnName("CreatorId");

                    b.Property<int>("UserType")
                        .HasColumnType("int");

                    b.HasKey("UserId", "DepartmentId", "RoleId");

                    b.HasIndex("DepartmentId");

                    b.HasIndex("RoleId");

                    b.HasIndex("UserId");

                    b.ToTable("Platform_UserDepartmentRoles", (string)null);
                });

            modelBuilder.Entity("Inspur.Abp.Platform.Organizational.YkzOrganization", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    b.Property<long?>("CreateTime")
                        .HasColumnType("bigint");

                    b.Property<int?>("DisplayOrder")
                        .HasColumnType("int");

                    b.Property<string>("GovAddress")
                        .HasMaxLength(1024)
                        .HasColumnType("varchar(1024)")
                        .HasComment("单位地址");

                    b.Property<string>("GovBusinessStripCodes")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)")
                        .HasComment("条线Code列表来自数据字典");

                    b.Property<string>("GovDivisionCode")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)")
                        .HasComment("行政区划Code来自数据字典");

                    b.Property<string>("GovInstitutionLevelCode")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)")
                        .HasComment("机构单位级别来自数据字典");

                    b.Property<string>("GovShortName")
                        .HasMaxLength(256)
                        .HasColumnType("varchar(256)")
                        .HasComment("机构简称");

                    b.Property<int?>("IsDeleted")
                        .HasColumnType("int");

                    b.Property<int?>("IsEnable")
                        .HasColumnType("int");

                    b.Property<string>("Name")
                        .HasMaxLength(512)
                        .HasColumnType("varchar(512)")
                        .HasComment("机构全称");

                    b.Property<string>("OrgType")
                        .HasMaxLength(256)
                        .HasColumnType("varchar(256)")
                        .HasComment("机构类型");

                    b.Property<string>("OrganizationCode")
                        .HasMaxLength(256)
                        .HasColumnType("varchar(256)")
                        .HasComment("政务钉钉组织机构code");

                    b.Property<long?>("ParentId")
                        .HasColumnType("bigint");

                    b.Property<string>("ParentOrganizationCode")
                        .HasMaxLength(256)
                        .HasColumnType("varchar(256)")
                        .HasComment("父组织机构code");

                    b.Property<string>("Principal")
                        .HasMaxLength(256)
                        .HasColumnType("varchar(256)")
                        .HasComment("单位负责人");

                    b.Property<string>("Remark")
                        .HasMaxLength(1024)
                        .HasColumnType("varchar(1024)")
                        .HasComment("备注");

                    b.Property<long?>("UpdateTime")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.ToTable("Platform_YkzOrganizations", (string)null);
                });

            modelBuilder.Entity("Inspur.Abp.Platform.Organizational.YkzOrgUnit", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    b.Property<long?>("CreateTime")
                        .HasColumnType("bigint");

                    b.Property<int?>("DisplayOrder")
                        .HasColumnType("int");

                    b.Property<string>("GovAddress")
                        .HasMaxLength(1024)
                        .HasColumnType("varchar(1024)")
                        .HasComment("单位地址");

                    b.Property<string>("GovBusinessStripCodes")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)")
                        .HasComment("条线Code列表来自数据字典");

                    b.Property<string>("GovDivisionCode")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)")
                        .HasComment("行政区划Code来自数据字典");

                    b.Property<string>("GovInstitutionLevelCode")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)")
                        .HasComment("机构单位级别来自数据字典");

                    b.Property<string>("GovShortName")
                        .HasMaxLength(256)
                        .HasColumnType("varchar(256)")
                        .HasComment("机构简称");

                    b.Property<int?>("IsDeleted")
                        .HasColumnType("int");

                    b.Property<int?>("IsEnable")
                        .HasColumnType("int");

                    b.Property<string>("Name")
                        .HasMaxLength(512)
                        .HasColumnType("varchar(512)")
                        .HasComment("机构全称");

                    b.Property<string>("OrgType")
                        .HasMaxLength(256)
                        .HasColumnType("varchar(256)")
                        .HasComment("机构类型");

                    b.Property<string>("OrganizationCode")
                        .HasMaxLength(256)
                        .HasColumnType("varchar(256)")
                        .HasComment("政务钉钉组织机构code");

                    b.Property<long?>("ParentId")
                        .HasColumnType("bigint");

                    b.Property<string>("ParentOrganizationCode")
                        .HasMaxLength(256)
                        .HasColumnType("varchar(256)")
                        .HasComment("父组织机构code");

                    b.Property<string>("Principal")
                        .HasMaxLength(256)
                        .HasColumnType("varchar(256)")
                        .HasComment("单位负责人");

                    b.Property<string>("Remark")
                        .HasMaxLength(1024)
                        .HasColumnType("varchar(1024)")
                        .HasComment("备注");

                    b.Property<long?>("UpdateTime")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.ToTable("Platform_YkzOrgUnits", (string)null);
                });

            modelBuilder.Entity("Inspur.Abp.Platform.Questions.QuestionFeedback", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("char(36)");

                    b.Property<int>("Category")
                        .HasMaxLength(1)
                        .HasColumnType("int")
                        .HasComment("问题类型");

                    b.Property<string>("Contact")
                        .HasMaxLength(20)
                        .HasColumnType("varchar(20)")
                        .HasComment("联系方式");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("CreationTime");

                    b.Property<Guid?>("CreatorId")
                        .HasColumnType("char(36)")
                        .HasColumnName("CreatorId");

                    b.Property<Guid?>("DepartmentId")
                        .HasColumnType("char(36)");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(1000)
                        .HasColumnType("varchar(1000)")
                        .HasComment("反馈描述");

                    b.Property<bool>("Disabled")
                        .HasColumnType("tinyint(1)");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("LastModificationTime");

                    b.Property<Guid?>("LastModifierId")
                        .HasColumnType("char(36)")
                        .HasColumnName("LastModifierId");

                    b.Property<Guid?>("RegionId")
                        .HasColumnType("char(36)");

                    b.Property<string>("Result")
                        .HasColumnType("longtext");

                    b.Property<int>("Status")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("DepartmentId");

                    b.HasIndex("RegionId");

                    b.ToTable("Platform_QuestionFeedbacks", (string)null);
                });

            modelBuilder.Entity("Inspur.Abp.Platform.ReportLedgerNotices.ReportLedgerNotice", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("char(36)");

                    b.Property<Guid?>("AreaOrganizationUnitId")
                        .HasColumnType("char(36)");

                    b.Property<Guid?>("AreaReportUnitId")
                        .HasColumnType("char(36)");

                    b.Property<Guid>("BigDepartmentId")
                        .HasColumnType("char(36)")
                        .HasComment("办理人所在大部门id");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("CreationTime");

                    b.Property<Guid?>("CreatorId")
                        .HasColumnType("char(36)")
                        .HasColumnName("CreatorId");

                    b.Property<Guid?>("DeleterId")
                        .HasColumnType("char(36)")
                        .HasColumnName("DeleterId");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("DeletionTime");

                    b.Property<Guid>("DepartmentId")
                        .HasColumnType("char(36)")
                        .HasComment("创建任务的大部门id");

                    b.Property<string>("DepartmentName")
                        .HasColumnType("longtext")
                        .HasComment("创建任务的大部门名称");

                    b.Property<DateTime?>("EndTime")
                        .HasColumnType("datetime(6)");

                    b.Property<Guid?>("FlowId")
                        .HasColumnType("char(36)");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("tinyint(1)")
                        .HasDefaultValue(false)
                        .HasColumnName("IsDeleted");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("LastModificationTime");

                    b.Property<Guid?>("LastModifierId")
                        .HasColumnType("char(36)")
                        .HasColumnName("LastModifierId");

                    b.Property<Guid?>("LedgerDataId")
                        .HasColumnType("char(36)");

                    b.Property<Guid?>("LedgerId")
                        .HasColumnType("char(36)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(512)
                        .HasColumnType("varchar(512)")
                        .HasComment("报表任务，台账名称");

                    b.Property<Guid?>("PlanTaskId")
                        .HasColumnType("char(36)");

                    b.Property<bool>("PlanTaskIsStope")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("ProcessedUserId")
                        .HasColumnType("json")
                        .HasComment("办理人id");

                    b.Property<string>("ProcessedUserName")
                        .HasColumnType("longtext")
                        .HasComment("办理人名称");

                    b.Property<DateTime?>("ProcessingTime")
                        .HasColumnType("datetime(6)");

                    b.Property<int>("ReportLedgerType")
                        .HasColumnType("int");

                    b.Property<string>("ReportTableTemplateId")
                        .HasColumnType("json");

                    b.Property<Guid?>("ReportTaskId")
                        .HasColumnType("char(36)");

                    b.Property<int>("State")
                        .HasColumnType("int");

                    b.Property<Guid?>("UserDepartmentId")
                        .HasColumnType("char(36)")
                        .HasComment("办理人所在部门id");

                    b.HasKey("Id");

                    b.ToTable("Platform_ReportLedgerNotices", (string)null);
                });

            modelBuilder.Entity("Inspur.Abp.Platform.Routes.Route", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("char(36)");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("CreationTime");

                    b.Property<Guid?>("CreatorId")
                        .HasColumnType("char(36)")
                        .HasColumnName("CreatorId");

                    b.Property<Guid?>("DeleterId")
                        .HasColumnType("char(36)")
                        .HasColumnName("DeleterId");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("DeletionTime");

                    b.Property<string>("DisplayName")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("varchar(256)")
                        .HasComment("路由显示名称");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("tinyint(1)")
                        .HasDefaultValue(false)
                        .HasColumnName("IsDeleted");

                    b.Property<int>("JumpCount")
                        .HasColumnType("int")
                        .HasComment("跳转次数");

                    b.Property<string>("JumpUrl")
                        .IsRequired()
                        .HasMaxLength(512)
                        .HasColumnType("varchar(512)")
                        .HasComment("路由跳转地址");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("LastModificationTime");

                    b.Property<Guid?>("LastModifierId")
                        .HasColumnType("char(36)")
                        .HasColumnName("LastModifierId");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)")
                        .HasComment("路由名称，建议字母、数字，不要使用中文");

                    b.HasKey("Id");

                    b.ToTable("Platform_Routes", (string)null);
                });

            modelBuilder.Entity("Inspur.Abp.Platform.SyncRecords.SyncRecord", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("char(36)");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("RequestData")
                        .HasColumnType("longtext");

                    b.HasKey("Id");

                    b.ToTable("Platform_SyncRecords", (string)null);
                });

            modelBuilder.Entity("Inspur.Abp.Platform.Users.User", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("char(36)");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .HasMaxLength(40)
                        .HasColumnType("varchar(40)")
                        .HasColumnName("ConcurrencyStamp");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("CreationTime");

                    b.Property<Guid?>("CreatorId")
                        .HasColumnType("char(36)")
                        .HasColumnName("CreatorId");

                    b.Property<Guid?>("DeleterId")
                        .HasColumnType("char(36)")
                        .HasColumnName("DeleterId");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("DeletionTime");

                    b.Property<Guid?>("DepartmentId")
                        .HasColumnType("char(36)");

                    b.Property<string>("ExtraProperties")
                        .HasColumnType("longtext")
                        .HasColumnName("ExtraProperties");

                    b.Property<int?>("Gender")
                        .HasColumnType("int");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("tinyint(1)")
                        .HasDefaultValue(false)
                        .HasColumnName("IsDeleted");

                    b.Property<string>("Job")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)")
                        .HasComment("职位");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("LastModificationTime");

                    b.Property<Guid?>("LastModifierId")
                        .HasColumnType("char(36)")
                        .HasColumnName("LastModifierId");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(64)
                        .HasColumnType("varchar(64)")
                        .HasComment("员工姓名");

                    b.Property<string>("Phone")
                        .HasMaxLength(32)
                        .HasColumnType("varchar(32)")
                        .HasComment("员工手机号");

                    b.Property<int>("Sort")
                        .HasColumnType("int")
                        .HasComment("部门排序字段");

                    b.HasKey("Id");

                    b.HasIndex("DepartmentId");

                    b.ToTable("Platform_Users", (string)null);
                });

            modelBuilder.Entity("Inspur.Abp.Platform.Users.YbtUser", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("char(36)");

                    b.Property<string>("DepartmentName")
                        .HasMaxLength(512)
                        .HasColumnType("varchar(512)");

                    b.Property<string>("Email")
                        .HasMaxLength(256)
                        .HasColumnType("varchar(256)");

                    b.Property<int?>("Gender")
                        .HasColumnType("int");

                    b.Property<string>("Job")
                        .HasColumnType("longtext")
                        .HasComment("职位");

                    b.Property<string>("Name")
                        .HasColumnType("longtext")
                        .HasComment("员工姓名");

                    b.Property<string>("Phone")
                        .HasColumnType("longtext")
                        .HasComment("员工手机号");

                    b.Property<string>("Role")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<string>("Signature")
                        .HasColumnType("longtext")
                        .HasComment("所有字段序列化后的签名");

                    b.Property<int>("Sort")
                        .HasColumnType("int")
                        .HasComment("部门排序字段");

                    b.Property<string>("UserName")
                        .IsRequired()
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.HasKey("Id");

                    b.ToTable("Platform_YbtUsers", (string)null);
                });

            modelBuilder.Entity("Inspur.Abp.Platform.WFTasks.WFDataProcess", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("char(36)");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("CreationTime");

                    b.Property<Guid?>("CreatorId")
                        .HasColumnType("char(36)")
                        .HasColumnName("CreatorId");

                    b.Property<string>("CurrentNodeHandlerDepartmentIds")
                        .HasColumnType("json")
                        .HasComment("当前流程节点处理人所属部门Id");

                    b.Property<string>("CurrentNodeHandlerIds")
                        .HasColumnType("json")
                        .HasComment("当前流程节点处理人Id");

                    b.Property<string>("CurrentNodeHandlerLargeDepartmentIds")
                        .HasColumnType("json")
                        .HasComment("当前流程节点处理人所属大部门显示Id");

                    b.Property<string>("CurrentNodeHandlerNames")
                        .HasColumnType("json")
                        .HasComment("当前流程节点处理人显示名称");

                    b.Property<string>("CurrentNodeId")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)")
                        .HasComment("当前流程节点Id");

                    b.Property<string>("CurrentNodeName")
                        .HasMaxLength(256)
                        .HasColumnType("varchar(256)")
                        .HasComment("当前流程节点名称");

                    b.Property<DateTime?>("CurrentNodeStartTime")
                        .HasColumnType("datetime(6)");

                    b.Property<int>("DataCount")
                        .HasColumnType("int");

                    b.Property<Guid>("DataFillerDepartmentId")
                        .HasColumnType("char(36)")
                        .HasComment("数据填报人所属部门Id");

                    b.Property<Guid>("DataFillerId")
                        .HasMaxLength(128)
                        .HasColumnType("char(128)")
                        .HasComment("数据填报人Id");

                    b.Property<Guid>("DataFillerLargeDepartmentId")
                        .HasColumnType("char(36)");

                    b.Property<string>("DataFillerName")
                        .IsRequired()
                        .HasColumnType("longtext")
                        .HasComment("数据填报人显示名称");

                    b.Property<Guid?>("DeleterId")
                        .HasColumnType("char(36)")
                        .HasColumnName("DeleterId");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("DeletionTime");

                    b.Property<int>("FillProgress")
                        .HasColumnType("int")
                        .HasComment("填报进度：0-100");

                    b.Property<int>("FillStatus")
                        .HasColumnType("int");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("tinyint(1)")
                        .HasDefaultValue(false)
                        .HasColumnName("IsDeleted");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("LastModificationTime");

                    b.Property<Guid?>("LastModifierId")
                        .HasColumnType("char(36)")
                        .HasColumnName("LastModifierId");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("varchar(256)")
                        .HasComment("数据流程名称");

                    b.Property<string>("ProcessId")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<Guid>("TaskItemId")
                        .HasColumnType("char(36)")
                        .HasComment("所属任务项Id");

                    b.HasKey("Id");

                    b.HasIndex("ProcessId");

                    b.HasIndex("TaskItemId");

                    b.ToTable("Platform_WFDataProcesses", (string)null);
                });

            modelBuilder.Entity("Inspur.Abp.Platform.WFTasks.WFPlanTask", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("char(36)");

                    b.Property<string>("BindObjectDisplayName")
                        .HasMaxLength(512)
                        .HasColumnType("varchar(512)")
                        .HasComment("绑定对象显示名称，如报表名称、台账名称");

                    b.Property<string>("BindObjectId")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)")
                        .HasComment("绑定对象Id,如报表Id、台账Id");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .HasMaxLength(40)
                        .HasColumnType("varchar(40)")
                        .HasColumnName("ConcurrencyStamp");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("CreationTime");

                    b.Property<Guid>("CreatorDepartmentId")
                        .HasColumnType("char(36)")
                        .HasComment("创建人部门Id");

                    b.Property<string>("CreatorDepartmentName")
                        .IsRequired()
                        .HasMaxLength(512)
                        .HasColumnType("varchar(512)")
                        .HasComment("创建者部门显示名称，格式：部门1-部门2");

                    b.Property<Guid?>("CreatorId")
                        .HasColumnType("char(36)")
                        .HasColumnName("CreatorId");

                    b.Property<Guid>("CreatorLargeDepartmentId")
                        .HasColumnType("char(36)")
                        .HasComment("创建人大部门Id");

                    b.Property<Guid>("CreatorUserId")
                        .HasColumnType("char(36)")
                        .HasComment("创建人Id");

                    b.Property<string>("CreatorUserName")
                        .IsRequired()
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)")
                        .HasComment("创建人显示名称");

                    b.Property<Guid?>("DeleterId")
                        .HasColumnType("char(36)")
                        .HasColumnName("DeleterId");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("DeletionTime");

                    b.Property<string>("Description")
                        .HasColumnType("longtext")
                        .HasComment("任务描述");

                    b.Property<string>("ExtraProperties")
                        .HasColumnType("longtext")
                        .HasColumnName("ExtraProperties");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("tinyint(1)")
                        .HasDefaultValue(false)
                        .HasColumnName("IsDeleted");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("LastModificationTime");

                    b.Property<Guid?>("LastModifierId")
                        .HasColumnType("char(36)")
                        .HasColumnName("LastModifierId");

                    b.Property<Guid?>("LatestTaskItemId")
                        .HasColumnType("char(36)")
                        .HasComment("最近的任务项Id");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("varchar(256)")
                        .HasComment("计划任务名称");

                    b.Property<int>("Status")
                        .HasColumnType("int");

                    b.Property<string>("TaskType")
                        .IsRequired()
                        .HasMaxLength(64)
                        .HasColumnType("varchar(64)")
                        .HasComment("任务类型,目前支持：报表、业务表");

                    b.HasKey("Id");

                    b.HasIndex("CreatorDepartmentId");

                    b.HasIndex("CreatorLargeDepartmentId");

                    b.HasIndex("CreatorUserId");

                    b.HasIndex("LatestTaskItemId");

                    b.ToTable("Platform_WFPlanTasks", (string)null);
                });

            modelBuilder.Entity("Inspur.Abp.Platform.WFTasks.WFTaskItem", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("char(36)");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .HasMaxLength(40)
                        .HasColumnType("varchar(40)")
                        .HasColumnName("ConcurrencyStamp");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("CreationTime");

                    b.Property<Guid?>("CreatorId")
                        .HasColumnType("char(36)")
                        .HasColumnName("CreatorId");

                    b.Property<int>("DataCount")
                        .HasColumnType("int")
                        .HasComment("数据更新总量");

                    b.Property<DateTime?>("Deadline")
                        .HasColumnType("datetime(6)");

                    b.Property<Guid?>("DeleterId")
                        .HasColumnType("char(36)")
                        .HasColumnName("DeleterId");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("DeletionTime");

                    b.Property<int>("ExecutionIntervalType")
                        .HasColumnType("int");

                    b.Property<string>("ExtraProperties")
                        .HasColumnType("longtext")
                        .HasColumnName("ExtraProperties");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("tinyint(1)")
                        .HasDefaultValue(false)
                        .HasColumnName("IsDeleted");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("LastModificationTime");

                    b.Property<Guid?>("LastModifierId")
                        .HasColumnType("char(36)")
                        .HasColumnName("LastModifierId");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("varchar(256)")
                        .HasComment("任务项名称");

                    b.Property<Guid>("PlanTaskId")
                        .HasColumnType("char(36)")
                        .HasComment("所属计划任务Id");

                    b.Property<DateTime?>("StartTime")
                        .HasColumnType("datetime(6)");

                    b.Property<int>("Status")
                        .HasColumnType("int");

                    b.Property<DateTime?>("TerminateTime")
                        .HasColumnType("datetime(6)");

                    b.HasKey("Id");

                    b.HasIndex("PlanTaskId");

                    b.ToTable("Platform_WFTaskItems", (string)null);
                });

            modelBuilder.Entity("Inspur.Abp.Platform.Widgets.WebWidget", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("char(36)");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .HasMaxLength(40)
                        .HasColumnType("varchar(40)")
                        .HasColumnName("ConcurrencyStamp");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("CreationTime");

                    b.Property<Guid?>("CreatorId")
                        .HasColumnType("char(36)")
                        .HasColumnName("CreatorId");

                    b.Property<Guid?>("DeleterId")
                        .HasColumnType("char(36)")
                        .HasColumnName("DeleterId");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("DeletionTime");

                    b.Property<string>("Description")
                        .HasColumnType("longtext");

                    b.Property<string>("DisplayName")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("varchar(256)")
                        .HasComment("组件显示名称");

                    b.Property<bool>("Enabled")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("ExtraProperties")
                        .HasColumnType("longtext")
                        .HasColumnName("ExtraProperties");

                    b.Property<double>("Height")
                        .HasColumnType("double");

                    b.Property<string>("Icon")
                        .IsRequired()
                        .HasColumnType("longtext")
                        .HasComment("组件图标");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("tinyint(1)")
                        .HasDefaultValue(false)
                        .HasColumnName("IsDeleted");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("LastModificationTime");

                    b.Property<Guid?>("LastModifierId")
                        .HasColumnType("char(36)")
                        .HasColumnName("LastModifierId");

                    b.Property<double>("Left")
                        .HasColumnType("double");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("varchar(256)")
                        .HasComment("组件名称");

                    b.Property<int>("Sort")
                        .HasColumnType("int")
                        .HasComment("组件排序,越小越靠前");

                    b.Property<string>("Style")
                        .HasColumnType("longtext")
                        .HasComment("样式，由前端自定义");

                    b.Property<double>("Top")
                        .HasColumnType("double");

                    b.Property<string>("Type")
                        .HasMaxLength(256)
                        .HasColumnType("varchar(256)")
                        .HasComment("组件类型");

                    b.Property<string>("Url")
                        .HasMaxLength(512)
                        .HasColumnType("varchar(512)")
                        .HasComment("组件Url");

                    b.Property<double>("Width")
                        .HasColumnType("double");

                    b.HasKey("Id");

                    b.ToTable("Platform_WebWidgets", (string)null);
                });

            modelBuilder.Entity("Inspur.Abp.Platform.WorkToDoRecords.WorkToDoRecord", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("char(36)");

                    b.Property<Guid?>("AreaOrganizationUnitId")
                        .HasColumnType("char(36)");

                    b.Property<string>("BusinessExtend")
                        .HasColumnType("json");

                    b.Property<Guid>("BusinessId")
                        .HasColumnType("char(36)")
                        .HasComment("业务Id");

                    b.Property<string>("BusinessType")
                        .HasColumnType("varchar(255)")
                        .HasComment("业务类型");

                    b.Property<int>("Category")
                        .HasColumnType("int");

                    b.Property<DateTime?>("CompletedTime")
                        .HasColumnType("datetime(6)")
                        .HasComment("完成时间");

                    b.Property<string>("CreaterDepartmentName")
                        .HasColumnType("longtext")
                        .HasComment("创建部门名称");

                    b.Property<string>("CreaterLargeDepartmentName")
                        .HasColumnType("longtext")
                        .HasComment("创建大部们名称");

                    b.Property<string>("CreaterName")
                        .HasColumnType("longtext")
                        .HasComment("创建人名称");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("CreationTime");

                    b.Property<Guid?>("CreatorId")
                        .HasColumnType("char(36)")
                        .HasColumnName("CreatorId");

                    b.Property<Guid?>("DeleterId")
                        .HasColumnType("char(36)")
                        .HasColumnName("DeleterId");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("DeletionTime");

                    b.Property<DateTime?>("EndTime")
                        .HasColumnType("datetime(6)");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("tinyint(1)")
                        .HasDefaultValue(false)
                        .HasColumnName("IsDeleted");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("LastModificationTime");

                    b.Property<Guid?>("LastModifierId")
                        .HasColumnType("char(36)")
                        .HasColumnName("LastModifierId");

                    b.Property<string>("Name")
                        .HasColumnType("longtext")
                        .HasComment("记录名称");

                    b.Property<Guid?>("OperatorDepartmentId")
                        .HasColumnType("char(36)")
                        .HasComment("处理人部门所在Id");

                    b.Property<Guid>("OperatorId")
                        .HasColumnType("char(36)")
                        .HasComment("处理人Id");

                    b.Property<Guid>("OperatorLargeDepartmentId")
                        .HasColumnType("char(36)")
                        .HasComment("处理人大部门Id");

                    b.Property<Guid?>("ReportTaskAreaOrganizationUnitId")
                        .HasColumnType("char(36)");

                    b.Property<Guid?>("ReportTaskId")
                        .HasColumnType("char(36)");

                    b.Property<int>("Status")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("OperatorId", "OperatorLargeDepartmentId", "BusinessId", "BusinessType")
                        .HasDatabaseName("IX_WorkToDoRecord_Operator_Business");

                    b.ToTable("Platform_WorkToDoRecords", (string)null);
                });

            modelBuilder.Entity("Volo.Abp.Identity.IdentityClaimType", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("char(36)");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .HasMaxLength(40)
                        .HasColumnType("varchar(40)")
                        .HasColumnName("ConcurrencyStamp");

                    b.Property<string>("Description")
                        .HasMaxLength(256)
                        .HasColumnType("varchar(256)");

                    b.Property<string>("ExtraProperties")
                        .HasColumnType("longtext")
                        .HasColumnName("ExtraProperties");

                    b.Property<bool>("IsStatic")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("varchar(256)");

                    b.Property<string>("Regex")
                        .HasMaxLength(512)
                        .HasColumnType("varchar(512)");

                    b.Property<string>("RegexDescription")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<bool>("Required")
                        .HasColumnType("tinyint(1)");

                    b.Property<int>("ValueType")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.ToTable("AbpClaimTypes", (string)null);
                });

            modelBuilder.Entity("Volo.Abp.Identity.IdentityLinkUser", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("char(36)");

                    b.Property<Guid?>("SourceTenantId")
                        .HasColumnType("char(36)");

                    b.Property<Guid>("SourceUserId")
                        .HasColumnType("char(36)");

                    b.Property<Guid?>("TargetTenantId")
                        .HasColumnType("char(36)");

                    b.Property<Guid>("TargetUserId")
                        .HasColumnType("char(36)");

                    b.HasKey("Id");

                    b.HasIndex("SourceUserId", "SourceTenantId", "TargetUserId", "TargetTenantId")
                        .IsUnique();

                    b.ToTable("AbpLinkUsers", (string)null);
                });

            modelBuilder.Entity("Volo.Abp.Identity.IdentityRole", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("char(36)");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .HasMaxLength(40)
                        .HasColumnType("varchar(40)")
                        .HasColumnName("ConcurrencyStamp");

                    b.Property<string>("ExtraProperties")
                        .HasColumnType("longtext")
                        .HasColumnName("ExtraProperties");

                    b.Property<bool>("IsDefault")
                        .HasColumnType("tinyint(1)")
                        .HasColumnName("IsDefault");

                    b.Property<bool>("IsPublic")
                        .HasColumnType("tinyint(1)")
                        .HasColumnName("IsPublic");

                    b.Property<bool>("IsStatic")
                        .HasColumnType("tinyint(1)")
                        .HasColumnName("IsStatic");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("varchar(256)");

                    b.Property<string>("NormalizedName")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("varchar(256)");

                    b.Property<Guid?>("TenantId")
                        .HasColumnType("char(36)")
                        .HasColumnName("TenantId");

                    b.HasKey("Id");

                    b.HasIndex("NormalizedName");

                    b.ToTable("AbpRoles", (string)null);
                });

            modelBuilder.Entity("Volo.Abp.Identity.IdentityRoleClaim", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("char(36)");

                    b.Property<string>("ClaimType")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("varchar(256)");

                    b.Property<string>("ClaimValue")
                        .HasMaxLength(1024)
                        .HasColumnType("varchar(1024)");

                    b.Property<Guid>("RoleId")
                        .HasColumnType("char(36)");

                    b.Property<Guid?>("TenantId")
                        .HasColumnType("char(36)")
                        .HasColumnName("TenantId");

                    b.HasKey("Id");

                    b.HasIndex("RoleId");

                    b.ToTable("AbpRoleClaims", (string)null);
                });

            modelBuilder.Entity("Volo.Abp.Identity.IdentitySecurityLog", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("char(36)");

                    b.Property<string>("Action")
                        .HasMaxLength(96)
                        .HasColumnType("varchar(96)");

                    b.Property<string>("ApplicationName")
                        .HasMaxLength(96)
                        .HasColumnType("varchar(96)");

                    b.Property<string>("BrowserInfo")
                        .HasMaxLength(512)
                        .HasColumnType("varchar(512)");

                    b.Property<string>("ClientId")
                        .HasMaxLength(64)
                        .HasColumnType("varchar(64)");

                    b.Property<string>("ClientIpAddress")
                        .HasMaxLength(64)
                        .HasColumnType("varchar(64)");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .HasMaxLength(40)
                        .HasColumnType("varchar(40)")
                        .HasColumnName("ConcurrencyStamp");

                    b.Property<string>("CorrelationId")
                        .HasMaxLength(64)
                        .HasColumnType("varchar(64)");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("ExtraProperties")
                        .HasColumnType("longtext")
                        .HasColumnName("ExtraProperties");

                    b.Property<string>("Identity")
                        .HasMaxLength(96)
                        .HasColumnType("varchar(96)");

                    b.Property<Guid?>("TenantId")
                        .HasColumnType("char(36)")
                        .HasColumnName("TenantId");

                    b.Property<string>("TenantName")
                        .HasMaxLength(64)
                        .HasColumnType("varchar(64)");

                    b.Property<Guid?>("UserId")
                        .HasColumnType("char(36)");

                    b.Property<string>("UserName")
                        .HasMaxLength(256)
                        .HasColumnType("varchar(256)");

                    b.HasKey("Id");

                    b.HasIndex("TenantId", "Action");

                    b.HasIndex("TenantId", "ApplicationName");

                    b.HasIndex("TenantId", "Identity");

                    b.HasIndex("TenantId", "UserId");

                    b.ToTable("AbpSecurityLogs", (string)null);
                });

            modelBuilder.Entity("Volo.Abp.Identity.IdentityUser", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("char(36)");

                    b.Property<int>("AccessFailedCount")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasDefaultValue(0)
                        .HasColumnName("AccessFailedCount");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .HasMaxLength(40)
                        .HasColumnType("varchar(40)")
                        .HasColumnName("ConcurrencyStamp");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("CreationTime");

                    b.Property<Guid?>("CreatorId")
                        .HasColumnType("char(36)")
                        .HasColumnName("CreatorId");

                    b.Property<Guid?>("DeleterId")
                        .HasColumnType("char(36)")
                        .HasColumnName("DeleterId");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("DeletionTime");

                    b.Property<string>("Email")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("varchar(256)")
                        .HasColumnName("Email");

                    b.Property<bool>("EmailConfirmed")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("tinyint(1)")
                        .HasDefaultValue(false)
                        .HasColumnName("EmailConfirmed");

                    b.Property<string>("ExtraProperties")
                        .HasColumnType("longtext")
                        .HasColumnName("ExtraProperties");

                    b.Property<bool>("IsActive")
                        .HasColumnType("tinyint(1)")
                        .HasColumnName("IsActive");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("tinyint(1)")
                        .HasDefaultValue(false)
                        .HasColumnName("IsDeleted");

                    b.Property<bool>("IsExternal")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("tinyint(1)")
                        .HasDefaultValue(false)
                        .HasColumnName("IsExternal");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("LastModificationTime");

                    b.Property<Guid?>("LastModifierId")
                        .HasColumnType("char(36)")
                        .HasColumnName("LastModifierId");

                    b.Property<bool>("LockoutEnabled")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("tinyint(1)")
                        .HasDefaultValue(false)
                        .HasColumnName("LockoutEnabled");

                    b.Property<DateTimeOffset?>("LockoutEnd")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("Name")
                        .HasMaxLength(64)
                        .HasColumnType("varchar(64)")
                        .HasColumnName("Name");

                    b.Property<string>("NormalizedEmail")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("varchar(256)")
                        .HasColumnName("NormalizedEmail");

                    b.Property<string>("NormalizedUserName")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("varchar(256)")
                        .HasColumnName("NormalizedUserName");

                    b.Property<string>("PasswordHash")
                        .HasMaxLength(256)
                        .HasColumnType("varchar(256)")
                        .HasColumnName("PasswordHash");

                    b.Property<string>("PhoneNumber")
                        .HasMaxLength(16)
                        .HasColumnType("varchar(16)")
                        .HasColumnName("PhoneNumber");

                    b.Property<bool>("PhoneNumberConfirmed")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("tinyint(1)")
                        .HasDefaultValue(false)
                        .HasColumnName("PhoneNumberConfirmed");

                    b.Property<string>("SecurityStamp")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("varchar(256)")
                        .HasColumnName("SecurityStamp");

                    b.Property<string>("Surname")
                        .HasMaxLength(64)
                        .HasColumnType("varchar(64)")
                        .HasColumnName("Surname");

                    b.Property<Guid?>("TenantId")
                        .HasColumnType("char(36)")
                        .HasColumnName("TenantId");

                    b.Property<bool>("TwoFactorEnabled")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("tinyint(1)")
                        .HasDefaultValue(false)
                        .HasColumnName("TwoFactorEnabled");

                    b.Property<string>("UserName")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("varchar(256)")
                        .HasColumnName("UserName");

                    b.HasKey("Id");

                    b.HasIndex("Email");

                    b.HasIndex("NormalizedEmail");

                    b.HasIndex("NormalizedUserName");

                    b.HasIndex("UserName");

                    b.ToTable("AbpUsers", (string)null);
                });

            modelBuilder.Entity("Volo.Abp.Identity.IdentityUserClaim", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("char(36)");

                    b.Property<string>("ClaimType")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("varchar(256)");

                    b.Property<string>("ClaimValue")
                        .HasMaxLength(1024)
                        .HasColumnType("varchar(1024)");

                    b.Property<Guid?>("TenantId")
                        .HasColumnType("char(36)")
                        .HasColumnName("TenantId");

                    b.Property<Guid>("UserId")
                        .HasColumnType("char(36)");

                    b.HasKey("Id");

                    b.HasIndex("UserId");

                    b.ToTable("AbpUserClaims", (string)null);
                });

            modelBuilder.Entity("Volo.Abp.Identity.IdentityUserLogin", b =>
                {
                    b.Property<Guid>("UserId")
                        .HasColumnType("char(36)");

                    b.Property<string>("LoginProvider")
                        .HasMaxLength(64)
                        .HasColumnType("varchar(64)");

                    b.Property<string>("ProviderDisplayName")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<string>("ProviderKey")
                        .IsRequired()
                        .HasMaxLength(196)
                        .HasColumnType("varchar(196)");

                    b.Property<Guid?>("TenantId")
                        .HasColumnType("char(36)")
                        .HasColumnName("TenantId");

                    b.HasKey("UserId", "LoginProvider");

                    b.HasIndex("LoginProvider", "ProviderKey");

                    b.ToTable("AbpUserLogins", (string)null);
                });

            modelBuilder.Entity("Volo.Abp.Identity.IdentityUserOrganizationUnit", b =>
                {
                    b.Property<Guid>("OrganizationUnitId")
                        .HasColumnType("char(36)");

                    b.Property<Guid>("UserId")
                        .HasColumnType("char(36)");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("CreationTime");

                    b.Property<Guid?>("CreatorId")
                        .HasColumnType("char(36)")
                        .HasColumnName("CreatorId");

                    b.Property<Guid?>("TenantId")
                        .HasColumnType("char(36)")
                        .HasColumnName("TenantId");

                    b.HasKey("OrganizationUnitId", "UserId");

                    b.HasIndex("UserId", "OrganizationUnitId");

                    b.ToTable("AbpUserOrganizationUnits", (string)null);
                });

            modelBuilder.Entity("Volo.Abp.Identity.IdentityUserRole", b =>
                {
                    b.Property<Guid>("UserId")
                        .HasColumnType("char(36)");

                    b.Property<Guid>("RoleId")
                        .HasColumnType("char(36)");

                    b.Property<Guid?>("TenantId")
                        .HasColumnType("char(36)")
                        .HasColumnName("TenantId");

                    b.HasKey("UserId", "RoleId");

                    b.HasIndex("RoleId", "UserId");

                    b.ToTable("AbpUserRoles", (string)null);
                });

            modelBuilder.Entity("Volo.Abp.Identity.IdentityUserToken", b =>
                {
                    b.Property<Guid>("UserId")
                        .HasColumnType("char(36)");

                    b.Property<string>("LoginProvider")
                        .HasMaxLength(64)
                        .HasColumnType("varchar(64)");

                    b.Property<string>("Name")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<Guid?>("TenantId")
                        .HasColumnType("char(36)")
                        .HasColumnName("TenantId");

                    b.Property<string>("Value")
                        .HasColumnType("longtext");

                    b.HasKey("UserId", "LoginProvider", "Name");

                    b.ToTable("AbpUserTokens", (string)null);
                });

            modelBuilder.Entity("Volo.Abp.Identity.OrganizationUnit", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("char(36)");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(95)
                        .HasColumnType("varchar(95)")
                        .HasColumnName("Code");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .HasMaxLength(40)
                        .HasColumnType("varchar(40)")
                        .HasColumnName("ConcurrencyStamp");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("CreationTime");

                    b.Property<Guid?>("CreatorId")
                        .HasColumnType("char(36)")
                        .HasColumnName("CreatorId");

                    b.Property<Guid?>("DeleterId")
                        .HasColumnType("char(36)")
                        .HasColumnName("DeleterId");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("DeletionTime");

                    b.Property<string>("DisplayName")
                        .IsRequired()
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)")
                        .HasColumnName("DisplayName");

                    b.Property<string>("ExtraProperties")
                        .HasColumnType("longtext")
                        .HasColumnName("ExtraProperties");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("tinyint(1)")
                        .HasDefaultValue(false)
                        .HasColumnName("IsDeleted");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("LastModificationTime");

                    b.Property<Guid?>("LastModifierId")
                        .HasColumnType("char(36)")
                        .HasColumnName("LastModifierId");

                    b.Property<Guid?>("ParentId")
                        .HasColumnType("char(36)");

                    b.Property<Guid?>("TenantId")
                        .HasColumnType("char(36)")
                        .HasColumnName("TenantId");

                    b.HasKey("Id");

                    b.HasIndex("Code");

                    b.HasIndex("ParentId");

                    b.ToTable("AbpOrganizationUnits", (string)null);
                });

            modelBuilder.Entity("Volo.Abp.Identity.OrganizationUnitRole", b =>
                {
                    b.Property<Guid>("OrganizationUnitId")
                        .HasColumnType("char(36)");

                    b.Property<Guid>("RoleId")
                        .HasColumnType("char(36)");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("CreationTime");

                    b.Property<Guid?>("CreatorId")
                        .HasColumnType("char(36)")
                        .HasColumnName("CreatorId");

                    b.Property<Guid?>("TenantId")
                        .HasColumnType("char(36)")
                        .HasColumnName("TenantId");

                    b.HasKey("OrganizationUnitId", "RoleId");

                    b.HasIndex("RoleId", "OrganizationUnitId");

                    b.ToTable("AbpOrganizationUnitRoles", (string)null);
                });

            modelBuilder.Entity("Inspur.Abp.Platform.Charts.Chart", b =>
                {
                    b.HasOne("Inspur.Abp.Platform.Charts.ChartGroup", "ChartGroup")
                        .WithMany("Charts")
                        .HasForeignKey("ChartGroupId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("ChartGroup");
                });

            modelBuilder.Entity("Inspur.Abp.Platform.Charts.ChartDicData", b =>
                {
                    b.HasOne("Inspur.Abp.Platform.Charts.Chart", "Chart")
                        .WithMany("DicDatas")
                        .HasForeignKey("ChartId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Chart");
                });

            modelBuilder.Entity("Inspur.Abp.Platform.Charts.ChartListData", b =>
                {
                    b.HasOne("Inspur.Abp.Platform.Charts.Chart", "Chart")
                        .WithMany("ListDatas")
                        .HasForeignKey("ChartId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Chart");
                });

            modelBuilder.Entity("Inspur.Abp.Platform.Charts.ChartTimeFlowData", b =>
                {
                    b.HasOne("Inspur.Abp.Platform.Charts.Chart", "Chart")
                        .WithMany("TimeFlowDatas")
                        .HasForeignKey("ChartId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Chart");
                });

            modelBuilder.Entity("Inspur.Abp.Platform.Organizational.Department", b =>
                {
                    b.HasOne("Inspur.Abp.Platform.Organizational.Department", "DepartmentExtend")
                        .WithMany("DepartmentExtendChildren")
                        .HasForeignKey("DepartmentExtendId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("Inspur.Abp.Platform.Organizational.Department", "Parent")
                        .WithMany("Children")
                        .HasForeignKey("ParentId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("Inspur.Abp.Platform.Organizational.Region", "Region")
                        .WithMany("Departments")
                        .HasForeignKey("RegionId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("Inspur.Abp.Platform.Organizational.YkzOrgUnit", "YkzOrgUnit")
                        .WithOne()
                        .HasForeignKey("Inspur.Abp.Platform.Organizational.Department", "YkzOrgUnitId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.HasOne("Inspur.Abp.Platform.Organizational.YkzOrganization", "YkzOrganization")
                        .WithOne()
                        .HasForeignKey("Inspur.Abp.Platform.Organizational.Department", "YkzOrganizationId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.Navigation("DepartmentExtend");

                    b.Navigation("Parent");

                    b.Navigation("Region");

                    b.Navigation("YkzOrgUnit");

                    b.Navigation("YkzOrganization");
                });

            modelBuilder.Entity("Inspur.Abp.Platform.Organizational.DepartmentFavorite", b =>
                {
                    b.HasOne("Inspur.Abp.Platform.Organizational.DepartmentFavoriteGroup", "DepartmentFavoriteGroup")
                        .WithMany("DepartmentFavorites")
                        .HasForeignKey("DepartmentFavoriteGroupId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("DepartmentFavoriteGroup");
                });

            modelBuilder.Entity("Inspur.Abp.Platform.Organizational.DepartmentFavoriteGroup", b =>
                {
                    b.HasOne("Inspur.Abp.Platform.Users.User", "User")
                        .WithMany("DepartmentFavoriteGroups")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("User");
                });

            modelBuilder.Entity("Inspur.Abp.Platform.Organizational.DepartmentGroup", b =>
                {
                    b.HasOne("Inspur.Abp.Platform.Organizational.Department", "StartDepartment")
                        .WithMany()
                        .HasForeignKey("StartDepartmentId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.Navigation("StartDepartment");
                });

            modelBuilder.Entity("Inspur.Abp.Platform.Organizational.DepartmentGroupDepartment", b =>
                {
                    b.HasOne("Inspur.Abp.Platform.Organizational.DepartmentGroup", "DepartmentGroup")
                        .WithMany("DepartmentGroupDepartments")
                        .HasForeignKey("DepartmentGroupId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("Inspur.Abp.Platform.Organizational.Department", "Department")
                        .WithMany("DepartmentGroupDepartments")
                        .HasForeignKey("DepartmentId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.Navigation("Department");

                    b.Navigation("DepartmentGroup");
                });

            modelBuilder.Entity("Inspur.Abp.Platform.Organizational.Region", b =>
                {
                    b.HasOne("Inspur.Abp.Platform.Organizational.Region", "Parent")
                        .WithMany("Children")
                        .HasForeignKey("ParentId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.Navigation("Parent");
                });

            modelBuilder.Entity("Inspur.Abp.Platform.Organizational.RegionFavorite", b =>
                {
                    b.HasOne("Inspur.Abp.Platform.Organizational.RegionFavoriteGroup", "RegionFavoriteGroup")
                        .WithMany("RegionFavorites")
                        .HasForeignKey("RegionFavoriteGroupId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("RegionFavoriteGroup");
                });

            modelBuilder.Entity("Inspur.Abp.Platform.Organizational.RegionFavoriteGroup", b =>
                {
                    b.HasOne("Inspur.Abp.Platform.Users.User", "User")
                        .WithMany("RegionFavoriteGroups")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("User");
                });

            modelBuilder.Entity("Inspur.Abp.Platform.Organizational.UserDepartment", b =>
                {
                    b.HasOne("Inspur.Abp.Platform.Organizational.Department", "Department")
                        .WithMany("UserDepartments")
                        .HasForeignKey("DepartmentId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("Inspur.Abp.Platform.Users.User", "User")
                        .WithMany("UserDepartments")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.Navigation("Department");

                    b.Navigation("User");
                });

            modelBuilder.Entity("Inspur.Abp.Platform.Organizational.UserDepartmentRole", b =>
                {
                    b.HasOne("Inspur.Abp.Platform.Organizational.Department", "Department")
                        .WithMany("UserDepartmentRoles")
                        .HasForeignKey("DepartmentId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("Volo.Abp.Identity.IdentityRole", "Role")
                        .WithMany()
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Inspur.Abp.Platform.Users.User", "User")
                        .WithMany("UserDepartmentRoles")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.Navigation("Department");

                    b.Navigation("Role");

                    b.Navigation("User");
                });

            modelBuilder.Entity("Inspur.Abp.Platform.Questions.QuestionFeedback", b =>
                {
                    b.HasOne("Inspur.Abp.Platform.Organizational.Department", "Department")
                        .WithMany("QuestionFeedbacks")
                        .HasForeignKey("DepartmentId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("Inspur.Abp.Platform.Organizational.Region", "Region")
                        .WithMany("QuestionFeedbacks")
                        .HasForeignKey("RegionId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.OwnsOne("Inspur.Abp.Platform.Files.FileInfo", "Attachment1", b1 =>
                        {
                            b1.Property<Guid>("QuestionFeedbackId")
                                .HasColumnType("char(36)");

                            b1.Property<string>("Extension")
                                .HasMaxLength(64)
                                .HasColumnType("varchar(64)")
                                .HasColumnName("ImportFile1Extension")
                                .HasComment("导入文件扩展名");

                            b1.Property<string>("Name")
                                .IsRequired()
                                .HasMaxLength(256)
                                .HasColumnType("varchar(256)")
                                .HasColumnName("ImporteFile1Name")
                                .HasComment("导入文件名称");

                            b1.Property<string>("ObjectId")
                                .IsRequired()
                                .HasMaxLength(64)
                                .HasColumnType("varchar(64)")
                                .HasColumnName("ImportFile1ObjectId")
                                .HasComment("导入文件Id,对象存储中的文件Id");

                            b1.Property<string>("Path")
                                .HasMaxLength(128)
                                .HasColumnType("varchar(128)")
                                .HasColumnName("ImportPath1");

                            b1.Property<long>("Size")
                                .HasColumnType("bigint")
                                .HasColumnName("ImportFile1Size")
                                .HasComment("导入文件大小，单位：字节");

                            b1.HasKey("QuestionFeedbackId");

                            b1.ToTable("Platform_QuestionFeedbacks");

                            b1.WithOwner()
                                .HasForeignKey("QuestionFeedbackId");
                        });

                    b.OwnsOne("Inspur.Abp.Platform.Files.FileInfo", "Attachment2", b1 =>
                        {
                            b1.Property<Guid>("QuestionFeedbackId")
                                .HasColumnType("char(36)");

                            b1.Property<string>("Extension")
                                .HasMaxLength(64)
                                .HasColumnType("varchar(64)")
                                .HasColumnName("ImportFile2Extension")
                                .HasComment("导入文件扩展名");

                            b1.Property<string>("Name")
                                .IsRequired()
                                .HasMaxLength(256)
                                .HasColumnType("varchar(256)")
                                .HasColumnName("ImporteFile2Name")
                                .HasComment("导入文件名称");

                            b1.Property<string>("ObjectId")
                                .IsRequired()
                                .HasMaxLength(64)
                                .HasColumnType("varchar(64)")
                                .HasColumnName("ImportFile2ObjectId")
                                .HasComment("导入文件Id,对象存储中的文件Id");

                            b1.Property<string>("Path")
                                .HasMaxLength(128)
                                .HasColumnType("varchar(128)")
                                .HasColumnName("ImportPath2");

                            b1.Property<long>("Size")
                                .HasColumnType("bigint")
                                .HasColumnName("ImportFile2Size")
                                .HasComment("导入文件大小，单位：字节");

                            b1.HasKey("QuestionFeedbackId");

                            b1.ToTable("Platform_QuestionFeedbacks");

                            b1.WithOwner()
                                .HasForeignKey("QuestionFeedbackId");
                        });

                    b.OwnsOne("Inspur.Abp.Platform.Files.FileInfo", "Attachment3", b1 =>
                        {
                            b1.Property<Guid>("QuestionFeedbackId")
                                .HasColumnType("char(36)");

                            b1.Property<string>("Extension")
                                .HasMaxLength(64)
                                .HasColumnType("varchar(64)")
                                .HasColumnName("ImportFile3Extension")
                                .HasComment("导入文件扩展名");

                            b1.Property<string>("Name")
                                .IsRequired()
                                .HasMaxLength(256)
                                .HasColumnType("varchar(256)")
                                .HasColumnName("ImporteFile3Name")
                                .HasComment("导入文件名称");

                            b1.Property<string>("ObjectId")
                                .IsRequired()
                                .HasMaxLength(64)
                                .HasColumnType("varchar(64)")
                                .HasColumnName("ImportFile3ObjectId")
                                .HasComment("导入文件Id,对象存储中的文件Id");

                            b1.Property<string>("Path")
                                .HasMaxLength(128)
                                .HasColumnType("varchar(128)")
                                .HasColumnName("ImportPath3");

                            b1.Property<long>("Size")
                                .HasColumnType("bigint")
                                .HasColumnName("ImportFile3Size")
                                .HasComment("导入文件大小，单位：字节");

                            b1.HasKey("QuestionFeedbackId");

                            b1.ToTable("Platform_QuestionFeedbacks");

                            b1.WithOwner()
                                .HasForeignKey("QuestionFeedbackId");
                        });

                    b.OwnsOne("Inspur.Abp.Platform.Files.FileInfo", "Attachment4", b1 =>
                        {
                            b1.Property<Guid>("QuestionFeedbackId")
                                .HasColumnType("char(36)");

                            b1.Property<string>("Extension")
                                .HasMaxLength(64)
                                .HasColumnType("varchar(64)")
                                .HasColumnName("ImportFile4Extension")
                                .HasComment("导入文件扩展名");

                            b1.Property<string>("Name")
                                .IsRequired()
                                .HasMaxLength(256)
                                .HasColumnType("varchar(256)")
                                .HasColumnName("ImporteFile4Name")
                                .HasComment("导入文件名称");

                            b1.Property<string>("ObjectId")
                                .IsRequired()
                                .HasMaxLength(64)
                                .HasColumnType("varchar(64)")
                                .HasColumnName("ImportFile4ObjectId")
                                .HasComment("导入文件Id,对象存储中的文件Id");

                            b1.Property<string>("Path")
                                .HasMaxLength(128)
                                .HasColumnType("varchar(128)")
                                .HasColumnName("ImportPath4");

                            b1.Property<long>("Size")
                                .HasColumnType("bigint")
                                .HasColumnName("ImportFile4Size")
                                .HasComment("导入文件大小，单位：字节");

                            b1.HasKey("QuestionFeedbackId");

                            b1.ToTable("Platform_QuestionFeedbacks");

                            b1.WithOwner()
                                .HasForeignKey("QuestionFeedbackId");
                        });

                    b.OwnsOne("Inspur.Abp.Platform.Files.FileInfo", "Attachment5", b1 =>
                        {
                            b1.Property<Guid>("QuestionFeedbackId")
                                .HasColumnType("char(36)");

                            b1.Property<string>("Extension")
                                .HasMaxLength(64)
                                .HasColumnType("varchar(64)")
                                .HasColumnName("ImportFile5Extension")
                                .HasComment("导入文件扩展名");

                            b1.Property<string>("Name")
                                .IsRequired()
                                .HasMaxLength(256)
                                .HasColumnType("varchar(256)")
                                .HasColumnName("ImporteFile5Name")
                                .HasComment("导入文件名称");

                            b1.Property<string>("ObjectId")
                                .IsRequired()
                                .HasMaxLength(64)
                                .HasColumnType("varchar(64)")
                                .HasColumnName("ImportFile5ObjectId")
                                .HasComment("导入文件Id,对象存储中的文件Id");

                            b1.Property<string>("Path")
                                .HasMaxLength(128)
                                .HasColumnType("varchar(128)")
                                .HasColumnName("ImportPath5");

                            b1.Property<long>("Size")
                                .HasColumnType("bigint")
                                .HasColumnName("ImportFile5Size")
                                .HasComment("导入文件大小，单位：字节");

                            b1.HasKey("QuestionFeedbackId");

                            b1.ToTable("Platform_QuestionFeedbacks");

                            b1.WithOwner()
                                .HasForeignKey("QuestionFeedbackId");
                        });

                    b.Navigation("Attachment1");

                    b.Navigation("Attachment2");

                    b.Navigation("Attachment3");

                    b.Navigation("Attachment4");

                    b.Navigation("Attachment5");

                    b.Navigation("Department");

                    b.Navigation("Region");
                });

            modelBuilder.Entity("Inspur.Abp.Platform.Users.User", b =>
                {
                    b.HasOne("Inspur.Abp.Platform.Organizational.Department", null)
                        .WithMany("Users")
                        .HasForeignKey("DepartmentId");
                });

            modelBuilder.Entity("Inspur.Abp.Platform.WFTasks.WFDataProcess", b =>
                {
                    b.HasOne("Inspur.Abp.Platform.WFTasks.WFTaskItem", "TaskItem")
                        .WithMany("DataProcesses")
                        .HasForeignKey("TaskItemId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("TaskItem");
                });

            modelBuilder.Entity("Inspur.Abp.Platform.WFTasks.WFPlanTask", b =>
                {
                    b.HasOne("Inspur.Abp.Platform.Organizational.Department", "CreatorDepartment")
                        .WithMany()
                        .HasForeignKey("CreatorDepartmentId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Inspur.Abp.Platform.Organizational.Department", "CreatorLargeDepartment")
                        .WithMany()
                        .HasForeignKey("CreatorLargeDepartmentId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Inspur.Abp.Platform.WFTasks.WFTaskItem", "LatestTaskItem")
                        .WithMany()
                        .HasForeignKey("LatestTaskItemId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.OwnsOne("Inspur.Abp.Platform.WFTasks.TaskExecutionIntervalConfig", "ExecutionIntervalConfig", b1 =>
                        {
                            b1.Property<Guid>("WFPlanTaskId")
                                .HasColumnType("char(36)");

                            b1.Property<TimeOnly?>("DailyDeadlineTime")
                                .HasColumnType("time(6)")
                                .HasComment("每日执行截止时间00:00:00 to 23:59:59.9999999.");

                            b1.Property<TimeOnly?>("DailyExecutionTime")
                                .HasColumnType("time(6)")
                                .HasComment("每日执行时间00:00:00 to 23:59:59.9999999.");

                            b1.Property<int>("DownHalfMonthDateOnlyTime")
                                .HasColumnType("int")
                                .HasComment("下半月截止日期: 16-31");

                            b1.Property<int>("DownHalfMonthExecutionTime")
                                .HasColumnType("int")
                                .HasComment("下半月执行日期: 16-31");

                            b1.Property<DateOnly?>("DownHalfYearDateOnlyTime")
                                .HasColumnType("date")
                                .HasComment("下半年截止日期，0001年6月1日-0001年12月31日");

                            b1.Property<DateOnly?>("DownHalfYearExecutionTime")
                                .HasColumnType("date")
                                .HasComment("下半年执行日期，0001年6月1日-0001年12月31日");

                            b1.Property<int>("Interval")
                                .HasColumnType("int")
                                .HasComment("执行周期: 0=不执行，1=每天，2=每周，3=每月，4=每季度，5=每年");

                            b1.Property<int?>("MonthlyDeadlineDay")
                                .HasColumnType("int")
                                .HasComment("每月截止日期: 1-28");

                            b1.Property<int?>("MonthlyExecutionDay")
                                .HasColumnType("int")
                                .HasComment("每月执行日期: 1-28");

                            b1.Property<DateOnly?>("QuarterlyDeadlineDay1")
                                .HasColumnType("date")
                                .HasComment("第一季度截止日期，0001年1月1日-0001年3月31日");

                            b1.Property<DateOnly?>("QuarterlyDeadlineDay2")
                                .HasColumnType("date")
                                .HasComment("第二季度截止日期，0001年4月1日-0001年6月30日");

                            b1.Property<DateOnly?>("QuarterlyDeadlineDay3")
                                .HasColumnType("date")
                                .HasComment("第三季度截止日期，0001年7月1日-0001年9月30日");

                            b1.Property<DateOnly?>("QuarterlyDeadlineDay4")
                                .HasColumnType("date")
                                .HasComment("第四季度截止日期，0001年10月1日-0001年12月31日");

                            b1.Property<DateOnly?>("QuarterlyExecutionDay1")
                                .HasColumnType("date")
                                .HasComment("第一季度度执行日期，0001年1月1日-0001年3月31日");

                            b1.Property<DateOnly?>("QuarterlyExecutionDay2")
                                .HasColumnType("date")
                                .HasComment("第二季度执行日期，0001年4月1日-0001年6月30日");

                            b1.Property<DateOnly?>("QuarterlyExecutionDay3")
                                .HasColumnType("date")
                                .HasComment("第三季度执行日期，0001年7月1日-0001年9月30日");

                            b1.Property<DateOnly?>("QuarterlyExecutionDay4")
                                .HasColumnType("date")
                                .HasComment("第四季度执行日期，0001年10月1日-0001年12月31日");

                            b1.Property<int>("UpHalfMonthDateOnlyTime")
                                .HasColumnType("int")
                                .HasComment("上半月截止日期: 1-15");

                            b1.Property<int>("UpHalfMonthExecutionTime")
                                .HasColumnType("int")
                                .HasComment("上半月执行日期: 1-15");

                            b1.Property<DateOnly?>("UpHalfYearDateOnlyTime")
                                .HasColumnType("date")
                                .HasComment("上半年截止日期，0001年1月1日-0001年6月31日");

                            b1.Property<DateOnly?>("UpHalfYearExecutionTime")
                                .HasColumnType("date")
                                .HasComment("上半年执行日期，0001年1月1日-0001年6月31日");

                            b1.Property<int?>("WeeklyDeadlineDayOfWeek")
                                .HasColumnType("int")
                                .HasComment("每周截止天数: 1=周一，2=周二，3=周三，4=周四，5=周五，6=周六,7=周日");

                            b1.Property<int?>("WeeklyExecutionDayOfWeek")
                                .HasColumnType("int")
                                .HasComment("每周执行天数: 1=周一，2=周二，3=周三，4=周四，5=周五，6=周六,7=周日");

                            b1.Property<DateOnly?>("YearlyDeadlineDay")
                                .HasColumnType("date")
                                .HasComment("每年截止日期，0001年1月1日-0001年12月31日");

                            b1.Property<DateOnly?>("YearlyExecutionDay")
                                .HasColumnType("date")
                                .HasComment("每年执行日期，0001年1月1日-0001年12月31日");

                            b1.HasKey("WFPlanTaskId");

                            b1.ToTable("Platform_WFPlanTasks");

                            b1.WithOwner()
                                .HasForeignKey("WFPlanTaskId");
                        });

                    b.Navigation("CreatorDepartment");

                    b.Navigation("CreatorLargeDepartment");

                    b.Navigation("ExecutionIntervalConfig");

                    b.Navigation("LatestTaskItem");
                });

            modelBuilder.Entity("Inspur.Abp.Platform.WFTasks.WFTaskItem", b =>
                {
                    b.HasOne("Inspur.Abp.Platform.WFTasks.WFPlanTask", "PlanTask")
                        .WithMany("TaskItems")
                        .HasForeignKey("PlanTaskId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("PlanTask");
                });

            modelBuilder.Entity("Volo.Abp.Identity.IdentityRoleClaim", b =>
                {
                    b.HasOne("Volo.Abp.Identity.IdentityRole", null)
                        .WithMany("Claims")
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Volo.Abp.Identity.IdentityUserClaim", b =>
                {
                    b.HasOne("Volo.Abp.Identity.IdentityUser", null)
                        .WithMany("Claims")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Volo.Abp.Identity.IdentityUserLogin", b =>
                {
                    b.HasOne("Volo.Abp.Identity.IdentityUser", null)
                        .WithMany("Logins")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Volo.Abp.Identity.IdentityUserOrganizationUnit", b =>
                {
                    b.HasOne("Volo.Abp.Identity.OrganizationUnit", null)
                        .WithMany()
                        .HasForeignKey("OrganizationUnitId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Volo.Abp.Identity.IdentityUser", null)
                        .WithMany("OrganizationUnits")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Volo.Abp.Identity.IdentityUserRole", b =>
                {
                    b.HasOne("Volo.Abp.Identity.IdentityRole", null)
                        .WithMany()
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Volo.Abp.Identity.IdentityUser", null)
                        .WithMany("Roles")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Volo.Abp.Identity.IdentityUserToken", b =>
                {
                    b.HasOne("Volo.Abp.Identity.IdentityUser", null)
                        .WithMany("Tokens")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Volo.Abp.Identity.OrganizationUnit", b =>
                {
                    b.HasOne("Volo.Abp.Identity.OrganizationUnit", null)
                        .WithMany()
                        .HasForeignKey("ParentId");
                });

            modelBuilder.Entity("Volo.Abp.Identity.OrganizationUnitRole", b =>
                {
                    b.HasOne("Volo.Abp.Identity.OrganizationUnit", null)
                        .WithMany("Roles")
                        .HasForeignKey("OrganizationUnitId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Volo.Abp.Identity.IdentityRole", null)
                        .WithMany()
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Inspur.Abp.Platform.Charts.Chart", b =>
                {
                    b.Navigation("DicDatas");

                    b.Navigation("ListDatas");

                    b.Navigation("TimeFlowDatas");
                });

            modelBuilder.Entity("Inspur.Abp.Platform.Charts.ChartGroup", b =>
                {
                    b.Navigation("Charts");
                });

            modelBuilder.Entity("Inspur.Abp.Platform.Organizational.Department", b =>
                {
                    b.Navigation("Children");

                    b.Navigation("DepartmentExtendChildren");

                    b.Navigation("DepartmentGroupDepartments");

                    b.Navigation("QuestionFeedbacks");

                    b.Navigation("UserDepartmentRoles");

                    b.Navigation("UserDepartments");

                    b.Navigation("Users");
                });

            modelBuilder.Entity("Inspur.Abp.Platform.Organizational.DepartmentFavoriteGroup", b =>
                {
                    b.Navigation("DepartmentFavorites");
                });

            modelBuilder.Entity("Inspur.Abp.Platform.Organizational.DepartmentGroup", b =>
                {
                    b.Navigation("DepartmentGroupDepartments");
                });

            modelBuilder.Entity("Inspur.Abp.Platform.Organizational.Region", b =>
                {
                    b.Navigation("Children");

                    b.Navigation("Departments");

                    b.Navigation("QuestionFeedbacks");
                });

            modelBuilder.Entity("Inspur.Abp.Platform.Organizational.RegionFavoriteGroup", b =>
                {
                    b.Navigation("RegionFavorites");
                });

            modelBuilder.Entity("Inspur.Abp.Platform.Users.User", b =>
                {
                    b.Navigation("DepartmentFavoriteGroups");

                    b.Navigation("RegionFavoriteGroups");

                    b.Navigation("UserDepartmentRoles");

                    b.Navigation("UserDepartments");
                });

            modelBuilder.Entity("Inspur.Abp.Platform.WFTasks.WFPlanTask", b =>
                {
                    b.Navigation("TaskItems");
                });

            modelBuilder.Entity("Inspur.Abp.Platform.WFTasks.WFTaskItem", b =>
                {
                    b.Navigation("DataProcesses");
                });

            modelBuilder.Entity("Volo.Abp.Identity.IdentityRole", b =>
                {
                    b.Navigation("Claims");
                });

            modelBuilder.Entity("Volo.Abp.Identity.IdentityUser", b =>
                {
                    b.Navigation("Claims");

                    b.Navigation("Logins");

                    b.Navigation("OrganizationUnits");

                    b.Navigation("Roles");

                    b.Navigation("Tokens");
                });

            modelBuilder.Entity("Volo.Abp.Identity.OrganizationUnit", b =>
                {
                    b.Navigation("Roles");
                });
#pragma warning restore 612, 618
        }
    }
}
